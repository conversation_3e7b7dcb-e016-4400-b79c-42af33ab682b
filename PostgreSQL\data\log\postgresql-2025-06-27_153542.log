2025-06-27 15:35:42.866 +07 [4604] LOG:  database system was shut down at 2025-06-27 15:35:03 +07
2025-06-27 15:35:42.961 +07 [11056] LOG:  database system is ready to accept connections
2025-06-27 15:40:56.266 +07 [3588] ERROR:  operator does not exist: jsonb ~~ unknown at character 71
2025-06-27 15:40:56.266 +07 [3588] HINT:  No operator matches the given name and argument types. You might need to add explicit type casts.
2025-06-27 15:40:56.266 +07 [3588] STATEMENT:  SELECT id, name, active, parent_id, action FROM ir_ui_menu WHERE name LIKE '%Multi-Channel%' OR name LIKE '%multichannel%' ORDER BY id
2025-06-27 15:41:09.298 +07 [26048] ERROR:  relation "ir_actions_act_window" does not exist at character 33
2025-06-27 15:41:09.298 +07 [26048] STATEMENT:  SELECT id, name, res_model FROM ir_actions_act_window WHERE res_model LIKE '%multichannel%'
2025-06-27 15:41:20.859 +07 [10660] ERROR:  column "im_status" does not exist at character 50
2025-06-27 15:41:20.859 +07 [10660] STATEMENT:  UPDATE res_users SET im_status = 'offline' WHERE im_status != 'offline'
2025-06-27 15:43:56.965 +07 [15640] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 15:43:56.966 +07 [14276] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 15:43:56.966 +07 [16524] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 15:45:33.201 +07 [17960] ERROR:  operator does not exist: jsonb ~~ unknown at character 53
2025-06-27 15:45:33.201 +07 [17960] HINT:  No operator matches the given name and argument types. You might need to add explicit type casts.
2025-06-27 15:45:33.201 +07 [17960] STATEMENT:  SELECT name, category_id FROM res_groups WHERE name LIKE '%multichannel%'
2025-06-27 15:49:20.924 +07 [12264] ERROR:  could not serialize access due to concurrent update
2025-06-27 15:49:20.924 +07 [12264] STATEMENT:   UPDATE "bus_presence"
	                    SET "status" = "__tmp"."status"::VARCHAR
	                    FROM (VALUES (3, 'offline')) AS "__tmp"("id", "status")
	                    WHERE "bus_presence"."id" = "__tmp"."id"
	                
2025-06-27 15:50:05.588 +07 [18376] ERROR:  canceling statement due to lock timeout
2025-06-27 15:50:05.588 +07 [18376] STATEMENT:  DROP TABLE "multichannel_product_sync_wizard" CASCADE
2025-06-27 15:50:20.631 +07 [18376] ERROR:  canceling statement due to lock timeout
2025-06-27 15:50:20.631 +07 [18376] STATEMENT:  DROP TABLE "multichannel_product_sync_wizard" CASCADE
2025-06-27 15:50:35.672 +07 [18376] ERROR:  canceling statement due to lock timeout
2025-06-27 15:50:35.672 +07 [18376] STATEMENT:  DROP TABLE "multichannel_product_sync_wizard" CASCADE
2025-06-27 15:50:50.703 +07 [18376] ERROR:  canceling statement due to lock timeout
2025-06-27 15:50:50.703 +07 [18376] STATEMENT:  DROP TABLE "multichannel_product_sync_wizard" CASCADE
2025-06-27 15:51:05.739 +07 [18376] ERROR:  canceling statement due to lock timeout
2025-06-27 15:51:05.739 +07 [18376] STATEMENT:  DROP TABLE "multichannel_shop_sync_wizard" CASCADE
2025-06-27 15:51:20.777 +07 [18376] ERROR:  canceling statement due to lock timeout
2025-06-27 15:51:20.777 +07 [18376] STATEMENT:  DROP TABLE "multichannel_warehouse_mapping" CASCADE
2025-06-27 15:51:35.807 +07 [18376] ERROR:  canceling statement due to lock timeout
2025-06-27 15:51:35.807 +07 [18376] STATEMENT:  DROP TABLE "multichannel_warehouse_mapping" CASCADE
2025-06-27 15:51:50.842 +07 [18376] ERROR:  canceling statement due to lock timeout
2025-06-27 15:51:50.842 +07 [18376] STATEMENT:  DROP TABLE "multichannel_affiliate_transaction" CASCADE
2025-06-27 15:52:05.886 +07 [18376] ERROR:  canceling statement due to lock timeout
2025-06-27 15:52:05.886 +07 [18376] STATEMENT:  DROP TABLE "multichannel_affiliate" CASCADE
2025-06-27 15:52:20.922 +07 [18376] ERROR:  canceling statement due to lock timeout
2025-06-27 15:52:20.922 +07 [18376] STATEMENT:  DROP TABLE "multichannel_affiliate" CASCADE
2025-06-27 15:52:35.951 +07 [18376] ERROR:  canceling statement due to lock timeout
2025-06-27 15:52:35.951 +07 [18376] STATEMENT:  DROP TABLE "multichannel_affiliate" CASCADE
2025-06-27 15:52:50.982 +07 [18376] ERROR:  canceling statement due to lock timeout
2025-06-27 15:52:50.982 +07 [18376] STATEMENT:  DROP TABLE "multichannel_advertising" CASCADE
2025-06-27 15:53:06.023 +07 [18376] ERROR:  canceling statement due to lock timeout
2025-06-27 15:53:06.023 +07 [18376] STATEMENT:  DROP TABLE "multichannel_transaction" CASCADE
2025-06-27 15:53:21.057 +07 [18376] ERROR:  canceling statement due to lock timeout
2025-06-27 15:53:21.057 +07 [18376] STATEMENT:  DROP TABLE "multichannel_transaction" CASCADE
2025-06-27 15:53:36.092 +07 [18376] ERROR:  canceling statement due to lock timeout
2025-06-27 15:53:36.092 +07 [18376] STATEMENT:  DROP TABLE "multichannel_product_mapping" CASCADE
2025-06-27 15:53:51.157 +07 [18376] ERROR:  canceling statement due to lock timeout
2025-06-27 15:53:51.157 +07 [18376] STATEMENT:  DROP TABLE "multichannel_product_mapping" CASCADE
2025-06-27 15:54:06.197 +07 [18376] ERROR:  canceling statement due to lock timeout
2025-06-27 15:54:06.197 +07 [18376] STATEMENT:  DROP TABLE "multichannel_shop" CASCADE
2025-06-27 16:08:52.446 +07 [19356] ERROR:  relation "ir_sessions" does not exist at character 13
2025-06-27 16:08:52.446 +07 [19356] STATEMENT:  DELETE FROM ir_sessions
2025-06-27 16:09:08.533 +07 [18376] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:09:08.533 +07 [28080] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:09:08.534 +07 [2260] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:09:08.535 +07 [2260] LOG:  unexpected EOF on client connection with an open transaction
2025-06-27 16:09:08.535 +07 [17708] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:09:08.535 +07 [13320] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:09:08.536 +07 [17708] LOG:  unexpected EOF on client connection with an open transaction
2025-06-27 16:09:08.536 +07 [13320] LOG:  unexpected EOF on client connection with an open transaction
2025-06-27 16:09:08.537 +07 [25608] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:09:08.537 +07 [5900] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:09:08.537 +07 [6468] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:09:08.537 +07 [14392] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:09:08.537 +07 [5268] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:09:08.538 +07 [5900] LOG:  unexpected EOF on client connection with an open transaction
2025-06-27 16:09:08.538 +07 [14392] LOG:  unexpected EOF on client connection with an open transaction
2025-06-27 16:09:08.538 +07 [6468] LOG:  unexpected EOF on client connection with an open transaction
2025-06-27 16:09:08.538 +07 [5268] LOG:  unexpected EOF on client connection with an open transaction
2025-06-27 16:09:08.538 +07 [4604] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:09:08.539 +07 [17572] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:09:08.540 +07 [17572] LOG:  unexpected EOF on client connection with an open transaction
2025-06-27 16:09:08.540 +07 [13436] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:09:08.540 +07 [15440] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:09:08.540 +07 [27192] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:09:08.540 +07 [16860] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:15:10.678 +07 [18544] ERROR:  relation "ir_actions_act_window" does not exist at character 53
2025-06-27 16:15:10.678 +07 [18544] STATEMENT:  
	        SELECT name, res_model, type 
	        FROM ir_actions_act_window 
	        WHERE name ILIKE '%multichannel%'
	    
2025-06-27 16:15:26.130 +07 [7016] ERROR:  relation "ir_actions_actions" does not exist at character 57
2025-06-27 16:15:26.130 +07 [7016] STATEMENT:  
	        SELECT id, name, res_model, type 
	        FROM ir_actions_actions 
	        WHERE name ILIKE '%multichannel%'
	    
2025-06-27 16:15:36.496 +07 [22560] ERROR:  relation "ir_ui_menu_cache" does not exist at character 13
2025-06-27 16:15:36.496 +07 [22560] STATEMENT:  DELETE FROM ir_ui_menu_cache
2025-06-27 16:17:54.063 +07 [19652] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:17:54.064 +07 [15928] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:17:54.064 +07 [23192] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:29:41.631 +07 [16304] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:29:41.631 +07 [11120] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:29:41.631 +07 [13208] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:29:41.631 +07 [15632] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:29:41.631 +07 [16492] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:35:29.505 +07 [24952] ERROR:  invalid input syntax for type json at character 40
2025-06-27 16:35:29.505 +07 [24952] DETAIL:  Token "User" is invalid.
2025-06-27 16:35:29.505 +07 [24952] CONTEXT:  JSON data, line 1: User
2025-06-27 16:35:29.505 +07 [24952] STATEMENT:  SELECT id FROM res_groups WHERE name = 'User'
2025-06-27 16:37:40.528 +07 [20408] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:43:19.252 +07 [20468] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:43:19.252 +07 [12868] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:43:19.253 +07 [25208] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:43:19.253 +07 [16620] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:43:19.253 +07 [16436] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:43:19.253 +07 [16324] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:43:19.253 +07 [20664] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:43:19.253 +07 [9364] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:43:19.253 +07 [22292] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 17:10:52.468 +07 [21588] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 17:23:28.852 +07 [21580] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 17:23:28.852 +07 [18228] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 17:28:49.911 +07 [22376] ERROR:  could not obtain lock on row in relation "ir_cron"
2025-06-27 17:28:49.911 +07 [22376] STATEMENT:  SELECT * FROM ir_cron FOR UPDATE NOWAIT
2025-06-27 17:29:34.938 +07 [20876] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 17:29:34.938 +07 [15540] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 17:29:58.123 +07 [28432] ERROR:  could not obtain lock on row in relation "ir_cron"
2025-06-27 17:29:58.123 +07 [28432] STATEMENT:  SELECT * FROM ir_cron FOR UPDATE NOWAIT
2025-06-27 17:30:30.870 +07 [10316] ERROR:  could not serialize access due to concurrent update
2025-06-27 17:30:30.870 +07 [10316] STATEMENT:   UPDATE "ir_module_module"
	                    SET "state" = "__tmp"."state"::VARCHAR, "write_date" = "__tmp"."write_date"::timestamp, "write_uid" = "__tmp"."write_uid"::int4
	                    FROM (VALUES (687, 'to install', '2025-06-27 10:26:42.051426', 1)) AS "__tmp"("id", "state", "write_date", "write_uid")
	                    WHERE "ir_module_module"."id" = "__tmp"."id"
	                
2025-06-27 17:30:30.872 +07 [10316] LOG:  could not send data to client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 17:30:30.872 +07 [10316] STATEMENT:   UPDATE "ir_module_module"
	                    SET "state" = "__tmp"."state"::VARCHAR, "write_date" = "__tmp"."write_date"::timestamp, "write_uid" = "__tmp"."write_uid"::int4
	                    FROM (VALUES (687, 'to install', '2025-06-27 10:26:42.051426', 1)) AS "__tmp"("id", "state", "write_date", "write_uid")
	                    WHERE "ir_module_module"."id" = "__tmp"."id"
	                
2025-06-27 17:30:30.872 +07 [10316] FATAL:  connection to client lost
2025-06-27 17:35:21.934 +07 [26788] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 17:35:21.934 +07 [13440] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 17:46:09.929 +07 [26732] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 17:46:09.929 +07 [9452] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 18:12:04.923 +07 [14140] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 18:12:04.923 +07 [11360] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 18:33:11.068 +07 [5848] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 18:33:11.068 +07 [12812] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 18:41:15.929 +07 [976] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 18:41:15.929 +07 [11744] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 18:41:24.035 +07 [5760] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 18:41:24.036 +07 [16424] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 18:41:24.036 +07 [14508] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 18:41:24.036 +07 [14744] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 18:43:29.037 +07 [13892] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 18:43:29.037 +07 [22536] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 18:43:29.038 +07 [26260] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 18:44:15.017 +07 [12884] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 18:44:15.017 +07 [19996] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 18:44:15.017 +07 [22896] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 18:49:31.934 +07 [3128] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 18:49:31.935 +07 [3128] LOG:  unexpected EOF on client connection with an open transaction
2025-06-27 19:04:53.488 +07 [15768] ERROR:  could not serialize access due to concurrent update
2025-06-27 19:04:53.488 +07 [15768] STATEMENT:  SELECT * FROM ir_cron FOR UPDATE NOWAIT
2025-06-27 19:04:53.669 +07 [15768] ERROR:  current transaction is aborted, commands ignored until end of transaction block
2025-06-27 19:04:53.669 +07 [15768] STATEMENT:  SELECT * FROM ir_cron FOR UPDATE NOWAIT
2025-06-27 19:04:53.812 +07 [15768] ERROR:  current transaction is aborted, commands ignored until end of transaction block
2025-06-27 19:04:53.812 +07 [15768] STATEMENT:  SELECT "ir_ui_menu"."id" FROM "ir_ui_menu" WHERE (("ir_ui_menu"."active" = true) AND (COALESCE("ir_ui_menu"."name"->>'vi_VN', "ir_ui_menu"."name"->>'en_US') = 'SaaS Management')) ORDER BY "ir_ui_menu"."sequence"  , "ir_ui_menu"."id"  
2025-06-27 19:04:54.047 +07 [15768] ERROR:  current transaction is aborted, commands ignored until end of transaction block
2025-06-27 19:04:54.047 +07 [15768] STATEMENT:  SELECT "ir_ui_view"."id" FROM "ir_ui_view" WHERE (("ir_ui_view"."active" = true) AND ("ir_ui_view"."model" IN ('saas.client', 'saas.plan', 'saas.instance', 'saas.server'))) ORDER BY "ir_ui_view"."priority"  , "ir_ui_view"."name"  , "ir_ui_view"."id"  
2025-06-27 19:04:54.296 +07 [15768] ERROR:  current transaction is aborted, commands ignored until end of transaction block
2025-06-27 19:04:54.296 +07 [15768] STATEMENT:  SELECT "ir_act_window"."id" FROM "ir_act_window" WHERE ("ir_act_window"."res_model" IN ('saas.client', 'saas.plan', 'saas.instance', 'saas.server')) ORDER BY COALESCE("ir_act_window"."name"->>'vi_VN', "ir_act_window"."name"->>'en_US')  
2025-06-27 19:07:59.085 +07 [15768] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 19:07:59.086 +07 [18196] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 19:08:05.262 +07 [25872] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 19:08:05.262 +07 [8208] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 19:08:05.262 +07 [22460] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 19:08:05.262 +07 [11980] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 19:08:05.262 +07 [10336] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 19:08:05.263 +07 [1164] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 19:08:05.263 +07 [21956] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 19:08:05.264 +07 [11404] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 19:08:05.264 +07 [11496] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 19:21:37.345 +07 [13688] ERROR:  could not serialize access due to concurrent update
2025-06-27 19:21:37.345 +07 [13688] STATEMENT:  SELECT * FROM ir_cron FOR UPDATE NOWAIT
2025-06-27 19:21:37.462 +07 [13688] ERROR:  current transaction is aborted, commands ignored until end of transaction block
2025-06-27 19:21:37.462 +07 [13688] STATEMENT:  SELECT "ir_ui_menu"."id" FROM "ir_ui_menu" WHERE (("ir_ui_menu"."active" = true) AND (COALESCE("ir_ui_menu"."name"->>'vi_VN', "ir_ui_menu"."name"->>'en_US') = 'SaaS Management')) ORDER BY "ir_ui_menu"."sequence"  , "ir_ui_menu"."id"  
2025-06-27 19:21:37.610 +07 [13688] ERROR:  current transaction is aborted, commands ignored until end of transaction block
2025-06-27 19:21:37.610 +07 [13688] STATEMENT:  SELECT "ir_ui_view"."id" FROM "ir_ui_view" WHERE (("ir_ui_view"."active" = true) AND ("ir_ui_view"."model" IN ('saas.client', 'saas.plan', 'saas.instance', 'saas.server'))) ORDER BY "ir_ui_view"."priority"  , "ir_ui_view"."name"  , "ir_ui_view"."id"  
2025-06-27 19:21:37.776 +07 [13688] ERROR:  current transaction is aborted, commands ignored until end of transaction block
2025-06-27 19:21:37.776 +07 [13688] STATEMENT:  SELECT "ir_act_window"."id" FROM "ir_act_window" WHERE ("ir_act_window"."res_model" IN ('saas.client', 'saas.plan', 'saas.instance', 'saas.server')) ORDER BY COALESCE("ir_act_window"."name"->>'vi_VN', "ir_act_window"."name"->>'en_US')  
2025-06-27 20:24:38.664 +07 [17556] ERROR:  column "partner_id" contains null values
2025-06-27 20:24:38.664 +07 [17556] STATEMENT:  ALTER TABLE "saas_client" ALTER COLUMN "partner_id" SET NOT NULL
2025-06-27 20:24:38.669 +07 [17556] ERROR:  column "email" contains null values
2025-06-27 20:24:38.669 +07 [17556] STATEMENT:  ALTER TABLE "saas_client" ALTER COLUMN "email" SET NOT NULL
2025-06-27 20:28:09.377 +07 [17556] ERROR:  column "email" contains null values
2025-06-27 20:28:09.377 +07 [17556] STATEMENT:  ALTER TABLE "saas_client" ALTER COLUMN "email" SET NOT NULL
2025-06-27 20:40:13.556 +07 [17556] ERROR:  column "email" contains null values
2025-06-27 20:40:13.556 +07 [17556] STATEMENT:  ALTER TABLE "saas_client" ALTER COLUMN "email" SET NOT NULL
2025-06-27 20:40:13.593 +07 [17556] ERROR:  null value in column "model_id" violates not-null constraint
2025-06-27 20:40:13.593 +07 [17556] DETAIL:  Failing row contains (2197, null, 1, 1, 1, saas.plan.user, t, t, t, t, t, 2025-06-27 13:40:12.330481, 2025-06-27 13:40:12.330481).
2025-06-27 20:40:13.593 +07 [17556] STATEMENT:  INSERT INTO "ir_model_access" ("active", "create_date", "create_uid", "group_id", "name", "perm_create", "perm_read", "perm_unlink", "perm_write", "write_date", "write_uid") VALUES (true, '2025-06-27 13:40:12.330481', 1, 1, 'saas.plan.user', true, true, true, true, '2025-06-27 13:40:12.330481', 1) RETURNING "id"
2025-06-27 20:40:13.599 +07 [17556] ERROR:  null value in column "model_id" violates not-null constraint
2025-06-27 20:40:13.599 +07 [17556] DETAIL:  Failing row contains (2198, null, 1, 1, 1, saas.plan.user, t, t, t, t, t, 2025-06-27 13:40:12.330481, 2025-06-27 13:40:12.330481).
2025-06-27 20:40:13.599 +07 [17556] STATEMENT:  INSERT INTO "ir_model_access" ("active", "create_date", "create_uid", "group_id", "name", "perm_create", "perm_read", "perm_unlink", "perm_write", "write_date", "write_uid") VALUES (true, '2025-06-27 13:40:12.330481', 1, 1, 'saas.plan.user', true, true, true, true, '2025-06-27 13:40:12.330481', 1) RETURNING "id"
2025-06-27 20:40:24.915 +07 [25572] ERROR:  could not serialize access due to concurrent update
2025-06-27 20:40:24.915 +07 [25572] STATEMENT:   UPDATE "bus_presence"
	                    SET "status" = "__tmp"."status"::VARCHAR
	                    FROM (VALUES (4, 'offline')) AS "__tmp"("id", "status")
	                    WHERE "bus_presence"."id" = "__tmp"."id"
	                
2025-06-27 20:41:06.803 +07 [9440] ERROR:  column "email" contains null values
2025-06-27 20:41:06.803 +07 [9440] STATEMENT:  ALTER TABLE "saas_client" ALTER COLUMN "email" SET NOT NULL
2025-06-27 20:43:36.351 +07 [9440] ERROR:  column "email" contains null values
2025-06-27 20:43:36.351 +07 [9440] STATEMENT:  ALTER TABLE "saas_client" ALTER COLUMN "email" SET NOT NULL
2025-06-27 20:43:37.066 +07 [9440] ERROR:  column "email" contains null values
2025-06-27 20:43:37.066 +07 [9440] STATEMENT:  ALTER TABLE "saas_client" ALTER COLUMN "email" SET NOT NULL
2025-06-27 20:43:37.463 +07 [9440] ERROR:  column "email" contains null values
2025-06-27 20:43:37.463 +07 [9440] STATEMENT:  ALTER TABLE "saas_client" ALTER COLUMN "email" SET NOT NULL
2025-06-27 20:53:38.873 +07 [9440] ERROR:  column "email" contains null values
2025-06-27 20:53:38.873 +07 [9440] STATEMENT:  ALTER TABLE "saas_client" ALTER COLUMN "email" SET NOT NULL
2025-06-27 20:53:38.911 +07 [9440] ERROR:  null value in column "model_id" violates not-null constraint
2025-06-27 20:53:38.911 +07 [9440] DETAIL:  Failing row contains (2201, null, 1, 1, 1, saas.instance.user, t, t, t, t, t, 2025-06-27 13:53:37.532149, 2025-06-27 13:53:37.532149).
2025-06-27 20:53:38.911 +07 [9440] STATEMENT:  INSERT INTO "ir_model_access" ("active", "create_date", "create_uid", "group_id", "name", "perm_create", "perm_read", "perm_unlink", "perm_write", "write_date", "write_uid") VALUES (true, '2025-06-27 13:53:37.532149', 1, 1, 'saas.instance.user', true, true, true, true, '2025-06-27 13:53:37.532149', 1) RETURNING "id"
2025-06-27 20:53:38.918 +07 [9440] ERROR:  null value in column "model_id" violates not-null constraint
2025-06-27 20:53:38.918 +07 [9440] DETAIL:  Failing row contains (2202, null, 1, 1, 1, saas.instance.user, t, t, t, t, t, 2025-06-27 13:53:37.532149, 2025-06-27 13:53:37.532149).
2025-06-27 20:53:38.918 +07 [9440] STATEMENT:  INSERT INTO "ir_model_access" ("active", "create_date", "create_uid", "group_id", "name", "perm_create", "perm_read", "perm_unlink", "perm_write", "write_date", "write_uid") VALUES (true, '2025-06-27 13:53:37.532149', 1, 1, 'saas.instance.user', true, true, true, true, '2025-06-27 13:53:37.532149', 1) RETURNING "id"
2025-06-27 20:58:34.638 +07 [9440] ERROR:  column "email" contains null values
2025-06-27 20:58:34.638 +07 [9440] STATEMENT:  ALTER TABLE "saas_client" ALTER COLUMN "email" SET NOT NULL
2025-06-27 20:58:34.674 +07 [9440] ERROR:  null value in column "model_id" violates not-null constraint
2025-06-27 20:58:34.674 +07 [9440] DETAIL:  Failing row contains (2203, null, 1, 1, 1, saas.instance.user, t, t, t, t, t, 2025-06-27 13:58:33.176619, 2025-06-27 13:58:33.176619).
2025-06-27 20:58:34.674 +07 [9440] STATEMENT:  INSERT INTO "ir_model_access" ("active", "create_date", "create_uid", "group_id", "name", "perm_create", "perm_read", "perm_unlink", "perm_write", "write_date", "write_uid") VALUES (true, '2025-06-27 13:58:33.176619', 1, 1, 'saas.instance.user', true, true, true, true, '2025-06-27 13:58:33.176619', 1) RETURNING "id"
2025-06-27 20:58:34.680 +07 [9440] ERROR:  null value in column "model_id" violates not-null constraint
2025-06-27 20:58:34.680 +07 [9440] DETAIL:  Failing row contains (2204, null, 1, 1, 1, saas.instance.user, t, t, t, t, t, 2025-06-27 13:58:33.176619, 2025-06-27 13:58:33.176619).
2025-06-27 20:58:34.680 +07 [9440] STATEMENT:  INSERT INTO "ir_model_access" ("active", "create_date", "create_uid", "group_id", "name", "perm_create", "perm_read", "perm_unlink", "perm_write", "write_date", "write_uid") VALUES (true, '2025-06-27 13:58:33.176619', 1, 1, 'saas.instance.user', true, true, true, true, '2025-06-27 13:58:33.176619', 1) RETURNING "id"
2025-06-27 21:04:21.217 +07 [9440] ERROR:  column "email" contains null values
2025-06-27 21:04:21.217 +07 [9440] STATEMENT:  ALTER TABLE "saas_client" ALTER COLUMN "email" SET NOT NULL
2025-06-27 21:05:57.645 +07 [9440] ERROR:  column "email" contains null values
2025-06-27 21:05:57.645 +07 [9440] STATEMENT:  ALTER TABLE "saas_client" ALTER COLUMN "email" SET NOT NULL
2025-06-27 21:06:24.386 +07 [9440] ERROR:  column "email" contains null values
2025-06-27 21:06:24.386 +07 [9440] STATEMENT:  ALTER TABLE "saas_client" ALTER COLUMN "email" SET NOT NULL
2025-06-27 21:13:39.896 +07 [13688] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 21:13:39.897 +07 [22280] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 21:13:40.519 +07 [9440] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 21:13:40.520 +07 [17468] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 21:13:40.521 +07 [19180] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 21:13:40.521 +07 [13520] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 21:13:40.522 +07 [20332] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 21:13:40.522 +07 [13276] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 21:13:40.522 +07 [2892] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 21:13:40.522 +07 [7040] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 21:13:41.459 +07 [18412] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 21:13:41.459 +07 [14392] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 21:13:41.460 +07 [18412] LOG:  unexpected EOF on client connection with an open transaction
2025-06-27 21:15:20.375 +07 [17840] ERROR:  column "email" contains null values
2025-06-27 21:15:20.375 +07 [17840] STATEMENT:  ALTER TABLE "saas_client" ALTER COLUMN "email" SET NOT NULL
2025-06-27 21:46:15.576 +07 [28500] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 21:46:15.577 +07 [6800] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 21:46:15.578 +07 [13408] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 21:46:15.578 +07 [15492] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 21:46:15.578 +07 [7308] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 22:02:50.266 +07 [11048] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 22:02:50.266 +07 [2652] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 22:02:50.266 +07 [2572] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 22:03:20.042 +07 [12268] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 22:03:20.043 +07 [8848] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 22:03:20.043 +07 [9784] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 22:03:20.043 +07 [22484] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 22:03:20.043 +07 [13992] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 22:03:20.044 +07 [15668] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 22:03:20.044 +07 [15760] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 22:58:42.542 +07 [7700] ERROR:  null value in column "name" violates not-null constraint
2025-06-27 22:58:42.542 +07 [7700] DETAIL:  Failing row contains (1, null, 1, 1, null, null, null, null, draft, null, t, 2025-06-27 15:58:42.498731, 2025-06-27 15:58:42.498731, null).
2025-06-27 22:58:42.542 +07 [7700] STATEMENT:  INSERT INTO "saas_client" ("active", "create_date", "create_uid", "state", "write_date", "write_uid") VALUES (true, '2025-06-27 15:58:42.498731', 1, 'draft', '2025-06-27 15:58:42.498731', 1) RETURNING "id"
2025-06-27 22:58:48.632 +07 [7700] ERROR:  null value in column "name" violates not-null constraint
2025-06-27 22:58:48.632 +07 [7700] DETAIL:  Failing row contains (2, null, 1, 1, null, null, null, null, draft, null, t, 2025-06-27 15:58:48.631407, 2025-06-27 15:58:48.631407, null).
2025-06-27 22:58:48.632 +07 [7700] STATEMENT:  INSERT INTO "saas_client" ("active", "create_date", "create_uid", "state", "write_date", "write_uid") VALUES (true, '2025-06-27 15:58:48.631407', 1, 'draft', '2025-06-27 15:58:48.631407', 1) RETURNING "id"
2025-06-27 23:18:10.411 +07 [7700] ERROR:  null value in column "name" violates not-null constraint
2025-06-27 23:18:10.411 +07 [7700] DETAIL:  Failing row contains (3, null, 1, 1, null, null, null, null, draft, null, t, 2025-06-27 16:18:10.410088, 2025-06-27 16:18:10.410088, null).
2025-06-27 23:18:10.411 +07 [7700] STATEMENT:  INSERT INTO "saas_client" ("active", "create_date", "create_uid", "state", "write_date", "write_uid") VALUES (true, '2025-06-27 16:18:10.410088', 1, 'draft', '2025-06-27 16:18:10.410088', 1) RETURNING "id"
2025-06-27 23:22:23.314 +07 [7700] ERROR:  null value in column "name" violates not-null constraint
2025-06-27 23:22:23.314 +07 [7700] DETAIL:  Failing row contains (4, null, 1, 1, null, null, null, null, draft, null, t, 2025-06-27 16:22:23.300931, 2025-06-27 16:22:23.300931, null).
2025-06-27 23:22:23.314 +07 [7700] STATEMENT:  INSERT INTO "saas_client" ("active", "create_date", "create_uid", "state", "write_date", "write_uid") VALUES (true, '2025-06-27 16:22:23.300931', 1, 'draft', '2025-06-27 16:22:23.300931', 1) RETURNING "id"
2025-06-27 23:23:13.590 +07 [13372] ERROR:  could not serialize access due to concurrent update
2025-06-27 23:23:13.590 +07 [13372] STATEMENT:   UPDATE "bus_presence"
	                    SET "status" = "__tmp"."status"::VARCHAR
	                    FROM (VALUES (4, 'offline')) AS "__tmp"("id", "status")
	                    WHERE "bus_presence"."id" = "__tmp"."id"
	                
2025-06-28 00:02:40.810 +07 [33020] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 00:02:40.813 +07 [17092] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 00:02:40.813 +07 [28704] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 00:05:24.752 +07 [31272] ERROR:  could not serialize access due to concurrent update
2025-06-28 00:05:24.752 +07 [31272] STATEMENT:   UPDATE "bus_presence"
	                    SET "status" = "__tmp"."status"::VARCHAR
	                    FROM (VALUES (4, 'offline')) AS "__tmp"("id", "status")
	                    WHERE "bus_presence"."id" = "__tmp"."id"
	                
