2025-06-28 00:49:02.244 +07 [13696] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 00:49:02.244 +07 [33048] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 00:49:02.244 +07 [31836] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 00:52:23.143 +07 [30092] ERROR:  could not serialize access due to concurrent update
2025-06-28 00:52:23.143 +07 [30092] STATEMENT:   UPDATE "bus_presence"
	                    SET "status" = "__tmp"."status"::VARCHAR
	                    FROM (VALUES (4, 'offline')) AS "__tmp"("id", "status")
	                    WHERE "bus_presence"."id" = "__tmp"."id"
	                
2025-06-28 01:04:42.815 +07 [11160] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 01:04:42.817 +07 [14992] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 01:04:42.820 +07 [28968] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 01:04:42.820 +07 [8436] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 01:04:42.820 +07 [22488] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 01:04:42.821 +07 [33660] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 01:04:42.822 +07 [404] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 01:08:17.744 +07 [4212] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 01:08:17.746 +07 [10020] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 01:08:17.746 +07 [11084] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 01:12:01.655 +07 [4396] ERROR:  could not serialize access due to concurrent update
2025-06-28 01:12:01.655 +07 [4396] STATEMENT:   UPDATE "bus_presence"
	                    SET "status" = "__tmp"."status"::VARCHAR
	                    FROM (VALUES (4, 'offline')) AS "__tmp"("id", "status")
	                    WHERE "bus_presence"."id" = "__tmp"."id"
	                
2025-06-28 01:12:04.142 +07 [19936] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 01:12:17.597 +07 [24980] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 01:12:17.598 +07 [14924] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 01:12:17.599 +07 [4472] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 01:12:17.601 +07 [33464] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 01:12:17.601 +07 [6904] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 01:21:54.490 +07 [1660] ERROR:  could not serialize access due to concurrent update
2025-06-28 01:21:54.490 +07 [1660] STATEMENT:   UPDATE "bus_presence"
	                    SET "status" = "__tmp"."status"::VARCHAR
	                    FROM (VALUES (4, 'offline')) AS "__tmp"("id", "status")
	                    WHERE "bus_presence"."id" = "__tmp"."id"
	                
2025-06-28 10:31:56.944 +07 [35236] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 10:31:56.945 +07 [30896] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 10:31:56.945 +07 [33780] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 10:38:48.084 +07 [7720] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 10:38:48.084 +07 [34452] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 10:38:48.084 +07 [35172] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 10:50:52.976 +07 [5612] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 10:50:52.976 +07 [31664] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 10:50:52.976 +07 [8400] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 10:52:27.601 +07 [16896] ERROR:  could not serialize access due to concurrent update
2025-06-28 10:52:27.601 +07 [16896] STATEMENT:   UPDATE "bus_presence"
	                    SET "status" = "__tmp"."status"::VARCHAR
	                    FROM (VALUES (4, 'offline')) AS "__tmp"("id", "status")
	                    WHERE "bus_presence"."id" = "__tmp"."id"
	                
2025-06-28 10:57:00.371 +07 [34424] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 10:57:00.371 +07 [24756] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 10:57:00.371 +07 [8084] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 11:05:10.739 +07 [14600] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 11:05:10.739 +07 [17996] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 11:05:10.739 +07 [32616] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 11:15:16.540 +07 [33844] ERROR:  could not serialize access due to concurrent update
2025-06-28 11:15:16.540 +07 [33844] STATEMENT:   UPDATE "bus_presence"
	                    SET "status" = "__tmp"."status"::VARCHAR
	                    FROM (VALUES (4, 'offline')) AS "__tmp"("id", "status")
	                    WHERE "bus_presence"."id" = "__tmp"."id"
	                
2025-06-28 11:19:56.764 +07 [33828] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 11:19:56.764 +07 [8072] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 11:19:56.764 +07 [17452] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 11:19:56.765 +07 [33912] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 11:19:56.765 +07 [5612] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 11:19:56.766 +07 [9888] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 11:19:56.766 +07 [33712] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 11:19:56.766 +07 [29460] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 11:34:52.326 +07 [22312] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 11:34:52.326 +07 [29516] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 11:34:52.326 +07 [35304] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 11:34:52.326 +07 [8384] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 11:34:52.326 +07 [34188] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 11:34:52.327 +07 [13620] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 11:34:52.327 +07 [14988] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 11:34:52.327 +07 [25912] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 11:38:46.020 +07 [30024] ERROR:  could not serialize access due to concurrent update
2025-06-28 11:38:46.020 +07 [30024] STATEMENT:   UPDATE "bus_presence"
	                    SET "status" = "__tmp"."status"::VARCHAR
	                    FROM (VALUES (4, 'offline')) AS "__tmp"("id", "status")
	                    WHERE "bus_presence"."id" = "__tmp"."id"
	                
2025-06-28 11:41:39.732 +07 [7416] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 11:41:39.732 +07 [18400] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 11:41:39.732 +07 [33648] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 11:41:39.732 +07 [11628] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 11:41:39.732 +07 [27972] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 11:41:39.732 +07 [12140] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 11:45:53.155 +07 [7044] ERROR:  could not serialize access due to concurrent update
2025-06-28 11:45:53.155 +07 [7044] STATEMENT:   UPDATE "bus_presence"
	                    SET "status" = "__tmp"."status"::VARCHAR
	                    FROM (VALUES (4, 'offline')) AS "__tmp"("id", "status")
	                    WHERE "bus_presence"."id" = "__tmp"."id"
	                
2025-06-28 11:57:30.319 +07 [16268] FATAL:  password authentication failed for user "odoo"
2025-06-28 11:57:30.319 +07 [16268] DETAIL:  Role "odoo" does not exist.
	Connection matched pg_hba.conf line 86: "host    all             all             ::1/128                 md5"
2025-06-28 11:58:23.427 +07 [28652] FATAL:  password authentication failed for user "Lenovo"
2025-06-28 11:58:23.427 +07 [28652] DETAIL:  Role "Lenovo" does not exist.
	Connection matched pg_hba.conf line 86: "host    all             all             ::1/128                 md5"
2025-06-28 12:01:19.596 +07 [35012] FATAL:  password authentication failed for user "Lenovo"
2025-06-28 12:01:19.596 +07 [35012] DETAIL:  Role "Lenovo" does not exist.
	Connection matched pg_hba.conf line 86: "host    all             all             ::1/128                 md5"
2025-06-28 12:01:39.998 +07 [29116] FATAL:  password authentication failed for user "odoo"
2025-06-28 12:01:39.998 +07 [29116] DETAIL:  Role "odoo" does not exist.
	Connection matched pg_hba.conf line 86: "host    all             all             ::1/128                 md5"
2025-06-28 12:04:54.119 +07 [14156] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 12:04:54.120 +07 [14156] LOG:  unexpected EOF on client connection with an open transaction
2025-06-28 12:06:45.712 +07 [33436] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 12:08:52.183 +07 [34376] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 12:08:52.183 +07 [32780] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 12:09:29.559 +07 [20032] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 12:13:20.191 +07 [18688] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-28 12:44:24.166 +07 [18136] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
