#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script kiểm tra trạng thái module database_manager
"""

import xmlrpc.client

# C<PERSON><PERSON> hình kết nối
url = 'http://localhost:8069'
db = 'ecomplus'
username = 'admin'
password = 'admin'

def check_module_status():
    """Kiểm tra trạng thái module và menu"""
    
    print("🔍 Checking Database Manager Module Status...")
    print("=" * 60)
    
    try:
        # Kết nối đến Odoo
        common = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/common')
        uid = common.authenticate(db, username, password, {})
        
        if not uid:
            print("❌ Authentication failed!")
            return False
            
        print(f"✅ Connected to Odoo as user ID: {uid}")
        
        # Tạo object proxy
        models = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/object')
        
        # 1. <PERSON><PERSON>m tra module c<PERSON> được cài đặt không
        print("\n📦 Checking Module Installation...")
        module = models.execute_kw(db, uid, password,
            'ir.module.module', 'search_read',
            [[('name', '=', 'database_manager')]],
            {'fields': ['name', 'state', 'installed_version', 'summary']})
        
        if module:
            mod = module[0]
            print(f"✅ Module Found:")
            print(f"   - Name: {mod['name']}")
            print(f"   - State: {mod['state']}")
            print(f"   - Version: {mod.get('installed_version', 'N/A')}")
            print(f"   - Summary: {mod.get('summary', 'N/A')}")
            
            if mod['state'] != 'installed':
                print(f"⚠️  Module state is '{mod['state']}', not 'installed'")
                return False
        else:
            print("❌ Module not found in ir.module.module!")
            return False
        
        # 2. Kiểm tra user có quyền truy cập không
        print("\n🔒 Checking User Permissions...")
        user = models.execute_kw(db, uid, password,
            'res.users', 'read',
            [uid], {'fields': ['groups_id', 'name']})
        
        if user:
            user_groups = user[0]['groups_id']
            print(f"✅ User: {user[0]['name']}")
            print(f"   - Groups: {user_groups}")
            
            # Kiểm tra group database_manager
            db_manager_group = models.execute_kw(db, uid, password,
                'res.groups', 'search',
                [[('id', '=', 'database_manager.group_database_manager')]])
            
            if db_manager_group:
                print(f"✅ Database Manager group exists: {db_manager_group}")
                
                # Kiểm tra user có trong group không
                if db_manager_group[0] in user_groups:
                    print("✅ User has Database Manager permissions")
                else:
                    print("❌ User does NOT have Database Manager permissions")
                    return False
            else:
                print("❌ Database Manager group not found!")
                return False
        
        # 3. Kiểm tra menu có tồn tại không
        print("\n📋 Checking Menu Items...")
        
        # Kiểm tra root menu
        root_menu = models.execute_kw(db, uid, password,
            'ir.ui.menu', 'search_read',
            [[('id', '=', 'database_manager.menu_database_manager_root')]],
            {'fields': ['name', 'sequence', 'web_icon', 'parent_id', 'groups_id']})
        
        if root_menu:
            menu = root_menu[0]
            print(f"✅ Root Menu Found:")
            print(f"   - Name: {menu['name']}")
            print(f"   - Sequence: {menu['sequence']}")
            print(f"   - Icon: {menu['web_icon']}")
            print(f"   - Parent: {menu['parent_id']}")
            print(f"   - Groups: {menu['groups_id']}")
        else:
            print("❌ Root menu not found!")
            return False
        
        # Kiểm tra submenu
        submenus = models.execute_kw(db, uid, password,
            'ir.ui.menu', 'search_read',
            [[('parent_id', '=', 'database_manager.menu_database_manager_root')]],
            {'fields': ['name', 'sequence', 'action']})
        
        print(f"✅ Found {len(submenus)} submenus:")
        for submenu in submenus:
            print(f"   - {submenu['name']} (seq: {submenu['sequence']})")
        
        # 4. Kiểm tra actions
        print("\n🎬 Checking Actions...")
        actions = [
            'database_manager.action_database_manager',
            'database_manager.action_database_create_wizard',
            'database_manager.action_database_template',
            'database_manager.action_database_template_module',
            'database_manager.action_database_sync'
        ]
        
        for action_id in actions:
            action = models.execute_kw(db, uid, password,
                'ir.actions.act_window', 'search_read',
                [[('id', '=', action_id)]],
                {'fields': ['name', 'res_model']})
            
            if action:
                print(f"✅ Action: {action[0]['name']}")
            else:
                print(f"❌ Action missing: {action_id}")
                return False
        
        print("\n" + "=" * 60)
        print("🎉 All checks passed! Module should be visible in menu.")
        return True
        
    except Exception as e:
        print(f"❌ Error during checking: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = check_module_status()
    exit(0 if success else 1)
