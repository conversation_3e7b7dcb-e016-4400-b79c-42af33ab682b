#!/usr/bin/env python3
import xmlrpc.client
import sys

url = 'http://localhost:8069'
db = 'ecomplus'
username = 'admin'
password = 'admin'

try:
    print("🔍 Debugging Menu Issues...")
    print("=" * 50)
    
    common = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/common')
    uid = common.authenticate(db, username, password, {})
    
    if not uid:
        print("❌ Authentication failed!")
        sys.exit(1)
        
    print(f"✅ Connected as user ID: {uid}")
    
    models = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/object')
    
    # 1. Check all menus with "Database" in name
    print("\n📋 Searching for Database-related menus...")
    menus = models.execute_kw(db, uid, password,
        'ir.ui.menu', 'search_read',
        [[('name', 'ilike', 'database')]],
        {'fields': ['name', 'parent_id', 'sequence', 'groups_id', 'web_icon']})
    
    for menu in menus:
        print(f"Menu: {menu['name']}")
        print(f"  - Parent: {menu['parent_id']}")
        print(f"  - Sequence: {menu['sequence']}")
        print(f"  - Groups: {menu['groups_id']}")
        print(f"  - Icon: {menu['web_icon']}")
        print()
    
    # 2. Check test menu
    print("📋 Searching for Test menu...")
    test_menus = models.execute_kw(db, uid, password,
        'ir.ui.menu', 'search_read',
        [[('name', 'ilike', 'test')]],
        {'fields': ['name', 'parent_id', 'sequence']})
    
    for menu in test_menus:
        print(f"Test Menu: {menu['name']}")
        print(f"  - Parent: {menu['parent_id']}")
        print(f"  - Sequence: {menu['sequence']}")
        print()
    
    # 3. Check top-level menus (no parent)
    print("📋 Top-level menus (no parent)...")
    top_menus = models.execute_kw(db, uid, password,
        'ir.ui.menu', 'search_read',
        [[('parent_id', '=', False)]],
        {'fields': ['name', 'sequence', 'web_icon'], 'order': 'sequence'})
    
    print("Top-level menus:")
    for menu in top_menus:
        print(f"  - {menu['name']} (seq: {menu['sequence']}, icon: {menu['web_icon']})")
    
    # 4. Check user groups
    print("\n🔒 User groups...")
    user = models.execute_kw(db, uid, password,
        'res.users', 'read',
        [uid], {'fields': ['groups_id', 'name']})
    
    user_groups = user[0]['groups_id']
    print(f"User {user[0]['name']} has groups: {user_groups}")
    
    # Check database_manager group
    db_group = models.execute_kw(db, uid, password,
        'res.groups', 'search_read',
        [[('name', '=', 'Database Manager')]],
        {'fields': ['id', 'name']})
    
    if db_group:
        group_id = db_group[0]['id']
        print(f"Database Manager group ID: {group_id}")
        if group_id in user_groups:
            print("✅ User has Database Manager group")
        else:
            print("❌ User does NOT have Database Manager group")
    else:
        print("❌ Database Manager group not found")
        
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
