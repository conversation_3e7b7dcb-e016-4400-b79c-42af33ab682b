<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- Template C<PERSON> bản -->
        <record id="template_basic" model="database.template">
            <field name="name"><PERSON><PERSON> bản</field>
            <field name="description">Template c<PERSON> bản với các module thiết yếu</field>
            <field name="language">vi_VN</field>
            <field name="timezone">Asia/Ho_Chi_Minh</field>
            <field name="country_code">VN</field>
            <field name="install_demo_data" eval="False"/>
            <field name="is_default" eval="True"/>
        </record>
        
        <!-- Module cho template cơ bản -->
        <record id="template_basic_module_base" model="database.template.module">
            <field name="template_id" ref="template_basic"/>
            <field name="name">base</field>
            <field name="display_name">Base</field>
            <field name="description">Modu<PERSON> c<PERSON> bản c<PERSON></field>
            <field name="category">base</field>
            <field name="required" eval="True"/>
        </record>
        
        <record id="template_basic_module_web" model="database.template.module">
            <field name="template_id" ref="template_basic"/>
            <field name="name">web</field>
            <field name="display_name">Web</field>
            <field name="description">Giao diện web của Odoo</field>
            <field name="category">base</field>
            <field name="required" eval="True"/>
        </record>
        
        <record id="template_basic_module_mail" model="database.template.module">
            <field name="template_id" ref="template_basic"/>
            <field name="name">mail</field>
            <field name="display_name">Mail</field>
            <field name="description">Hệ thống email và tin nhắn</field>
            <field name="category">base</field>
            <field name="required" eval="False"/>
        </record>
        
        <!-- Template E-commerce -->
        <record id="template_ecommerce" model="database.template">
            <field name="name">E-commerce</field>
            <field name="description">Template cho website bán hàng trực tuyến</field>
            <field name="language">vi_VN</field>
            <field name="timezone">Asia/Ho_Chi_Minh</field>
            <field name="country_code">VN</field>
            <field name="install_demo_data" eval="True"/>
            <field name="is_default" eval="False"/>
        </record>
        
        <!-- Module cho template e-commerce -->
        <record id="template_ecommerce_module_base" model="database.template.module">
            <field name="template_id" ref="template_ecommerce"/>
            <field name="name">base</field>
            <field name="display_name">Base</field>
            <field name="description">Module cơ bản của Odoo</field>
            <field name="category">base</field>
            <field name="required" eval="True"/>
        </record>
        
        <record id="template_ecommerce_module_web" model="database.template.module">
            <field name="template_id" ref="template_ecommerce"/>
            <field name="name">web</field>
            <field name="display_name">Web</field>
            <field name="description">Giao diện web của Odoo</field>
            <field name="category">base</field>
            <field name="required" eval="True"/>
        </record>
        
        <record id="template_ecommerce_module_website" model="database.template.module">
            <field name="template_id" ref="template_ecommerce"/>
            <field name="name">website</field>
            <field name="display_name">Website</field>
            <field name="description">Website builder</field>
            <field name="category">website</field>
            <field name="required" eval="False"/>
        </record>
        
        <record id="template_ecommerce_module_website_sale" model="database.template.module">
            <field name="template_id" ref="template_ecommerce"/>
            <field name="name">website_sale</field>
            <field name="display_name">Website Sale</field>
            <field name="description">Bán hàng trực tuyến</field>
            <field name="category">website</field>
            <field name="required" eval="False"/>
        </record>
        
        <record id="template_ecommerce_module_sale" model="database.template.module">
            <field name="template_id" ref="template_ecommerce"/>
            <field name="name">sale</field>
            <field name="display_name">Sales</field>
            <field name="description">Quản lý bán hàng</field>
            <field name="category">sales</field>
            <field name="required" eval="False"/>
        </record>
        
        <record id="template_ecommerce_module_stock" model="database.template.module">
            <field name="template_id" ref="template_ecommerce"/>
            <field name="name">stock</field>
            <field name="display_name">Inventory</field>
            <field name="description">Quản lý kho vận</field>
            <field name="category">inventory</field>
            <field name="required" eval="False"/>
        </record>
        
        <record id="template_ecommerce_module_account" model="database.template.module">
            <field name="template_id" ref="template_ecommerce"/>
            <field name="name">account</field>
            <field name="display_name">Accounting</field>
            <field name="description">Kế toán</field>
            <field name="category">accounting</field>
            <field name="required" eval="False"/>
        </record>
        
        <!-- Template Kế toán -->
        <record id="template_accounting" model="database.template">
            <field name="name">Kế toán</field>
            <field name="description">Template cho hệ thống kế toán doanh nghiệp</field>
            <field name="language">vi_VN</field>
            <field name="timezone">Asia/Ho_Chi_Minh</field>
            <field name="country_code">VN</field>
            <field name="install_demo_data" eval="False"/>
            <field name="is_default" eval="False"/>
        </record>
        
        <!-- Module cho template kế toán -->
        <record id="template_accounting_module_base" model="database.template.module">
            <field name="template_id" ref="template_accounting"/>
            <field name="name">base</field>
            <field name="display_name">Base</field>
            <field name="description">Module cơ bản của Odoo</field>
            <field name="category">base</field>
            <field name="required" eval="True"/>
        </record>
        
        <record id="template_accounting_module_web" model="database.template.module">
            <field name="template_id" ref="template_accounting"/>
            <field name="name">web</field>
            <field name="display_name">Web</field>
            <field name="description">Giao diện web của Odoo</field>
            <field name="category">base</field>
            <field name="required" eval="True"/>
        </record>
        
        <record id="template_accounting_module_account" model="database.template.module">
            <field name="template_id" ref="template_accounting"/>
            <field name="name">account</field>
            <field name="display_name">Accounting</field>
            <field name="description">Kế toán</field>
            <field name="category">accounting</field>
            <field name="required" eval="False"/>
        </record>
        
        <record id="template_accounting_module_purchase" model="database.template.module">
            <field name="template_id" ref="template_accounting"/>
            <field name="name">purchase</field>
            <field name="display_name">Purchase</field>
            <field name="description">Quản lý mua hàng</field>
            <field name="category">purchase</field>
            <field name="required" eval="False"/>
        </record>
        
        <record id="template_accounting_module_sale" model="database.template.module">
            <field name="template_id" ref="template_accounting"/>
            <field name="name">sale</field>
            <field name="display_name">Sales</field>
            <field name="description">Quản lý bán hàng</field>
            <field name="category">sales</field>
            <field name="required" eval="False"/>
        </record>
        
    </data>
</odoo>
