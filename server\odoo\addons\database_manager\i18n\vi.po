# Translation of Odoo Server.
# This file contains the translation of the following modules:
#	* database_manager
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-27 10:00+0000\n"
"PO-Revision-Date: 2025-01-27 10:00+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: database_manager
#: model:ir.model.fields,field_description:database_manager.field_database_manager__admin_email
msgid "Admin Email"
msgstr "Email Admin"

#. module: database_manager
#: model:ir.model.fields,field_description:database_manager.field_database_manager__admin_login
msgid "Admin Login"
msgstr "Tên đ<PERSON>ng nh<PERSON>p Admin"

#. module: database_manager
#: model:ir.model.fields,field_description:database_manager.field_database_manager__admin_name
msgid "Admin Name"
msgstr "Tên Admin"

#. module: database_manager
#: model:ir.model.fields,field_description:database_manager.field_database_create_wizard__admin_password
msgid "Admin Password"
msgstr "Mật khẩu Admin"

#. module: database_manager
#: model:ir.model.fields,field_description:database_manager.field_database_manager__country_code
msgid "Country"
msgstr "Quốc gia"

#. module: database_manager
#: model:ir.model.fields,field_description:database_manager.field_database_manager__create_date
msgid "Created on"
msgstr "Ngày tạo"

#. module: database_manager
#: model:ir.model.fields,field_description:database_manager.field_database_manager__create_uid
msgid "Created by"
msgstr "Tạo bởi"

#. module: database_manager
#: model:ir.model.fields,field_description:database_manager.field_database_manager__name
msgid "Database Name"
msgstr "Tên Database"

#. module: database_manager
#: model:ir.model.fields,field_description:database_manager.field_database_manager__display_name
msgid "Display Name"
msgstr "Tên hiển thị"

#. module: database_manager
#: model:ir.model.fields,field_description:database_manager.field_database_manager__language
msgid "Language"
msgstr "Ngôn ngữ"

#. module: database_manager
#: model:ir.model.fields,field_description:database_manager.field_database_manager__notes
msgid "Notes"
msgstr "Ghi chú"

#. module: database_manager
#: model:ir.model.fields,field_description:database_manager.field_database_manager__size
msgid "Size (MB)"
msgstr "Kích thước (MB)"

#. module: database_manager
#: model:ir.model.fields,field_description:database_manager.field_database_manager__state
msgid "State"
msgstr "Trạng thái"

#. module: database_manager
#: model:ir.model.fields,field_description:database_manager.field_database_manager__timezone
msgid "Timezone"
msgstr "Múi giờ"

#. module: database_manager
#: model:ir.model.fields,field_description:database_manager.field_database_manager__write_date
msgid "Last Modified on"
msgstr "Sửa đổi lần cuối"

#. module: database_manager
#: model:ir.model.fields,field_description:database_manager.field_database_manager__write_uid
msgid "Last Modified by"
msgstr "Sửa đổi bởi"

#. module: database_manager
#: model:ir.model.fields,selection:database_manager.field_database_manager__state
msgid "Active"
msgstr "Hoạt động"

#. module: database_manager
#: model:ir.model.fields,selection:database_manager.field_database_manager__state
msgid "Inactive"
msgstr "Không hoạt động"

#. module: database_manager
#: model:ir.model.fields,selection:database_manager.field_database_manager__state
msgid "Creating"
msgstr "Đang tạo"

#. module: database_manager
#: model:ir.model.fields,selection:database_manager.field_database_manager__state
msgid "Error"
msgstr "Lỗi"

#. module: database_manager
#: model:ir.actions.act_window,name:database_manager.action_database_manager
#: model:ir.ui.menu,name:database_manager.menu_database_manager
msgid "Database Manager"
msgstr "Quản lý Database"

#. module: database_manager
#: model:ir.actions.act_window,name:database_manager.action_database_template
#: model:ir.ui.menu,name:database_manager.menu_database_template
msgid "Database Templates"
msgstr "Template Database"

#. module: database_manager
#: model:ir.ui.menu,name:database_manager.menu_database_main
msgid "Database Management"
msgstr "Quản lý Database"

#. module: database_manager
#: model:ir.ui.menu,name:database_manager.menu_database_config
msgid "Configuration"
msgstr "Cấu hình"

#. module: database_manager
#: model:ir.ui.menu,name:database_manager.menu_database_tools
msgid "Tools"
msgstr "Công cụ"

#. module: database_manager
#: code:addons/database_manager/models/database_manager.py:0
msgid "Cannot delete current database!"
msgstr "Không thể xóa database hiện tại!"

#. module: database_manager
#: code:addons/database_manager/wizard/database_create_wizard.py:0
msgid "Master password is incorrect!"
msgstr "Master password không đúng!"

#. module: database_manager
#: code:addons/database_manager/wizard/database_create_wizard.py:0
msgid "Error creating database: %s"
msgstr "Lỗi khi tạo database: %s"

#. module: database_manager
#: code:addons/database_manager/wizard/database_delete_wizard.py:0
msgid "Error deleting database: %s"
msgstr "Lỗi khi xóa database: %s"

#. module: database_manager
#: code:addons/database_manager/wizard/database_delete_wizard.py:0
msgid "Database '%s' does not exist!"
msgstr "Database '%s' không tồn tại!"

#. module: database_manager
#: model:ir.actions.act_window,name:database_manager.action_database_create_wizard
msgid "Create Database"
msgstr "Tạo Database"

#. module: database_manager
#: model:ir.actions.act_window,name:database_manager.action_database_delete_wizard
msgid "Delete Database"
msgstr "Xóa Database"

#. module: database_manager
#: model:ir.actions.act_window,name:database_manager.action_database_sync_wizard
msgid "Sync Databases"
msgstr "Đồng bộ Database"

#. module: database_manager
#: model:ir.model.fields,field_description:database_manager.field_database_create_wizard__database_name
msgid "Database Name"
msgstr "Tên Database"

#. module: database_manager
#: model:ir.model.fields,field_description:database_manager.field_database_create_wizard__database_display_name
msgid "Display Name"
msgstr "Tên hiển thị"

#. module: database_manager
#: model:ir.model.fields,field_description:database_manager.field_database_create_wizard__master_password
msgid "Master Password"
msgstr "Master Password"

#. module: database_manager
#: model:ir.model.fields,field_description:database_manager.field_database_create_wizard__install_demo_data
msgid "Install Demo Data"
msgstr "Cài đặt dữ liệu mẫu"

#. module: database_manager
#: model:ir.model.fields,field_description:database_manager.field_database_template__name
msgid "Template Name"
msgstr "Tên Template"

#. module: database_manager
#: model:ir.model.fields,field_description:database_manager.field_database_template__description
msgid "Description"
msgstr "Mô tả"

#. module: database_manager
#: model:ir.model.fields,field_description:database_manager.field_database_template__is_default
msgid "Default Template"
msgstr "Template mặc định"

#. module: database_manager
#: model:ir.model.fields,field_description:database_manager.field_database_template_module__name
msgid "Module Name"
msgstr "Tên Module"

#. module: database_manager
#: model:ir.model.fields,field_description:database_manager.field_database_template_module__required
msgid "Required"
msgstr "Bắt buộc"

#. module: database_manager
#: model:ir.model.fields,selection:database_manager.field_database_template_module__category
msgid "Sales"
msgstr "Bán hàng"

#. module: database_manager
#: model:ir.model.fields,selection:database_manager.field_database_template_module__category
msgid "Purchase"
msgstr "Mua hàng"

#. module: database_manager
#: model:ir.model.fields,selection:database_manager.field_database_template_module__category
msgid "Inventory"
msgstr "Kho vận"

#. module: database_manager
#: model:ir.model.fields,selection:database_manager.field_database_template_module__category
msgid "Accounting"
msgstr "Kế toán"

#. module: database_manager
#: model:ir.model.fields,selection:database_manager.field_database_template_module__category
msgid "Human Resources"
msgstr "Nhân sự"

#. module: database_manager
#: model:ir.model.fields,selection:database_manager.field_database_template_module__category
msgid "Project"
msgstr "Dự án"

#. module: database_manager
#: model:ir.model.fields,selection:database_manager.field_database_template_module__category
msgid "Manufacturing"
msgstr "Sản xuất"

#. module: database_manager
#: model:ir.model.fields,selection:database_manager.field_database_template_module__category
msgid "Website"
msgstr "Website"

#. module: database_manager
#: model:ir.model.fields,selection:database_manager.field_database_template_module__category
msgid "Base"
msgstr "Cơ bản"

#. module: database_manager
#: model:ir.model.fields,selection:database_manager.field_database_template_module__category
msgid "Other"
msgstr "Khác"

#. module: database_manager
#: view:database.manager:database_manager.view_database_manager_form
msgid "Refresh Info"
msgstr "Làm mới thông tin"

#. module: database_manager
#: view:database.manager:database_manager.view_database_manager_form
msgid "Delete Database"
msgstr "Xóa Database"

#. module: database_manager
#: view:database.manager:database_manager.view_database_manager_list
msgid "Create Database"
msgstr "Tạo Database"

#. module: database_manager
#: view:database.manager:database_manager.view_database_manager_list
msgid "Sync Databases"
msgstr "Đồng bộ Database"

#. module: database_manager
#: view:database.template:database_manager.view_database_template_form
msgid "Apply Template"
msgstr "Áp dụng Template"

#. module: database_manager
#: view:database.create.wizard:database_manager.view_database_create_wizard_form
msgid "Create"
msgstr "Tạo"

#. module: database_manager
#: view:database.create.wizard:database_manager.view_database_create_wizard_form
msgid "Cancel"
msgstr "Hủy"

#. module: database_manager
#: view:database.delete.wizard:database_manager.view_database_delete_wizard_form
msgid "Delete"
msgstr "Xóa"

#. module: database_manager
#: view:database.sync.wizard:database_manager.view_database_sync_wizard_form
msgid "Sync"
msgstr "Đồng bộ"

#. module: database_manager
#: code:addons/database_manager/wizard/database_create_wizard.py:0
msgid "Success"
msgstr "Thành công"

#. module: database_manager
#: code:addons/database_manager/wizard/database_create_wizard.py:0
msgid "Database \"%s\" has been created successfully!"
msgstr "Database \"%s\" đã được tạo thành công!"

#. module: database_manager
#: code:addons/database_manager/wizard/database_delete_wizard.py:0
msgid "Database \"%s\" has been deleted successfully!"
msgstr "Database \"%s\" đã được xóa thành công!"
