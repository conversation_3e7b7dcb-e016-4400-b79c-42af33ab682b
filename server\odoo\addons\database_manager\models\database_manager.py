# -*- coding: utf-8 -*-

import logging
import psycopg2
from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
from odoo.service import db
from odoo.tools import config

_logger = logging.getLogger(__name__)


class DatabaseManager(models.Model):
    _name = 'database.manager'
    _description = 'Quản lý Database'
    _order = 'create_date desc'

    name = fields.Char(
        string='Tên Database',
        required=True,
        help='Tên của database'
    )
    display_name = fields.Char(
        string='Tên hiển thị',
        help='Tên hiển thị cho database'
    )
    state = fields.Selection([
        ('active', 'Hoạt động'),
        ('inactive', 'Không hoạt động'),
        ('creating', 'Đang tạo'),
        ('error', 'Lỗi')
    ], string='Trạng thái', default='active', required=True)
    
    size = fields.Float(
        string='<PERSON><PERSON><PERSON> thước (MB)',
        help='<PERSON>ích thước database tính bằng MB'
    )
    created_date = fields.Datetime(
        string='Ngày tạo',
        default=fields.Datetime.now,
        help='Ngày tạo database'
    )
    last_backup_date = fields.Datetime(
        string='Backup cuối cùng',
        help='Ngày backup cuối cùng'
    )
    
    # Thông tin admin user
    admin_login = fields.Char(
        string='Admin Login',
        default='admin',
        help='Tên đăng nhập của admin'
    )
    admin_email = fields.Char(
        string='Admin Email',
        help='Email của admin'
    )
    admin_name = fields.Char(
        string='Admin Name',
        help='Tên đầy đủ của admin'
    )
    
    # Thông tin cấu hình
    language = fields.Selection([
        ('vi_VN', 'Tiếng Việt'),
        ('en_US', 'English'),
        ('fr_FR', 'Français'),
        ('de_DE', 'Deutsch'),
        ('es_ES', 'Español'),
    ], string='Ngôn ngữ', default='vi_VN')
    
    timezone = fields.Selection([
        ('Asia/Ho_Chi_Minh', 'Việt Nam (UTC+7)'),
        ('UTC', 'UTC'),
        ('America/New_York', 'New York (UTC-5)'),
        ('Europe/London', 'London (UTC+0)'),
        ('Asia/Tokyo', 'Tokyo (UTC+9)'),
    ], string='Múi giờ', default='Asia/Ho_Chi_Minh')
    
    country_code = fields.Char(
        string='Mã quốc gia',
        default='VN',
        help='Mã quốc gia (VN, US, FR, etc.)'
    )
    
    # Module được cài đặt
    installed_modules = fields.Text(
        string='Module đã cài',
        help='Danh sách module đã được cài đặt'
    )
    
    # Ghi chú
    notes = fields.Text(
        string='Ghi chú',
        help='Ghi chú về database'
    )
    
    # Computed fields
    is_current_db = fields.Boolean(
        string='Database hiện tại',
        compute='_compute_is_current_db',
        help='Database hiện tại đang sử dụng'
    )
    
    @api.depends('name')
    def _compute_is_current_db(self):
        """Kiểm tra xem có phải database hiện tại không"""
        current_db = self.env.cr.dbname
        for record in self:
            record.is_current_db = (record.name == current_db)
    
    @api.model
    def get_database_list(self):
        """Lấy danh sách tất cả database từ PostgreSQL"""
        try:
            db_list = db.list_dbs(force=True)
            return db_list
        except Exception as e:
            _logger.error(f"Lỗi khi lấy danh sách database: {e}")
            return []
    
    @api.model
    def sync_databases(self):
        """Đồng bộ danh sách database từ PostgreSQL"""
        try:
            db_list = self.get_database_list()
            existing_dbs = self.search([]).mapped('name')
            
            # Thêm database mới
            for db_name in db_list:
                if db_name not in existing_dbs:
                    self.create({
                        'name': db_name,
                        'display_name': db_name,
                        'state': 'active',
                        'size': self._get_database_size(db_name),
                    })
            
            # Cập nhật trạng thái database không còn tồn tại
            for record in self.search([('name', 'not in', db_list)]):
                record.state = 'inactive'
                
            return True
        except Exception as e:
            _logger.error(f"Lỗi khi đồng bộ database: {e}")
            raise UserError(_("Lỗi khi đồng bộ database: %s") % str(e))
    
    def _get_database_size(self, db_name):
        """Lấy kích thước database"""
        try:
            # Kết nối đến database để lấy kích thước
            db_host = config.get('db_host', 'localhost')
            db_port = config.get('db_port', 5432)
            db_user = config.get('db_user', 'odoo')
            db_password = config.get('db_password', '')
            
            conn = psycopg2.connect(
                host=db_host,
                port=db_port,
                user=db_user,
                password=db_password,
                database='postgres'
            )
            
            with conn.cursor() as cursor:
                cursor.execute("""
                    SELECT pg_size_pretty(pg_database_size(%s))::text,
                           pg_database_size(%s)
                """, (db_name, db_name))
                result = cursor.fetchone()
                if result:
                    # Chuyển đổi từ bytes sang MB
                    size_bytes = result[1]
                    size_mb = size_bytes / (1024 * 1024)
                    return round(size_mb, 2)
            
            conn.close()
            return 0.0
        except Exception as e:
            _logger.warning(f"Không thể lấy kích thước database {db_name}: {e}")
            return 0.0
    
    def action_refresh_info(self):
        """Làm mới thông tin database"""
        for record in self:
            if record.name in self.get_database_list():
                record.size = record._get_database_size(record.name)
                record.state = 'active'
            else:
                record.state = 'inactive'
    
    def action_delete_database(self):
        """Xóa database với xác nhận"""
        for record in self:
            if record.is_current_db:
                raise UserError(_("Không thể xóa database hiện tại đang sử dụng!"))
            
            # Hiển thị wizard xác nhận xóa
            return {
                'name': _('Xác nhận xóa Database'),
                'type': 'ir.actions.act_window',
                'res_model': 'database.delete.wizard',
                'view_mode': 'form',
                'target': 'new',
                'context': {'default_database_id': record.id}
            }

    @api.model
    def action_sync_databases(self):
        """Đồng bộ danh sách database"""
        return {
            'type': 'ir.actions.act_window',
            'name': _('Đồng bộ Database'),
            'res_model': 'database.sync.wizard',
            'view_mode': 'form',
            'target': 'new',
        }
    
    @api.constrains('name')
    def _check_database_name(self):
        """Kiểm tra tên database hợp lệ"""
        import re
        for record in self:
            if record.name:
                # Kiểm tra pattern tên database (chỉ chữ, số, gạch dưới, gạch ngang, dấu chấm)
                if not re.match(r'^[a-zA-Z0-9_.-]+$', record.name):
                    raise ValidationError(_(
                        "Tên database chỉ được chứa chữ cái, số, dấu gạch dưới (_), "
                        "dấu gạch ngang (-) và dấu chấm (.)"
                    ))
