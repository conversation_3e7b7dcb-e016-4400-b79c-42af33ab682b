# -*- coding: utf-8 -*-

from odoo import models, fields, api, _


class DatabaseTemplate(models.Model):
    _name = 'database.template'
    _description = 'Template cấu hình Database'
    _order = 'name'

    name = fields.Char(
        string='Tên Template',
        required=True,
        help='Tên của template cấu hình'
    )
    description = fields.Text(
        string='Mô tả',
        help='Mô tả về template này'
    )
    
    # Cấu hình cơ bản
    language = fields.Selection([
        ('vi_VN', 'Tiếng Việt'),
        ('en_US', 'English'),
        ('fr_FR', 'Français'),
        ('de_DE', 'Deutsch'),
        ('es_ES', 'Español'),
    ], string='Ngôn ngữ mặc định', default='vi_VN')
    
    timezone = fields.Selection([
        ('Asia/Ho_Chi_Minh', 'Việt Nam (UTC+7)'),
        ('UTC', 'UTC'),
        ('America/New_York', 'New York (UTC-5)'),
        ('Europe/London', 'London (UTC+0)'),
        ('Asia/Tokyo', 'Tokyo (UTC+9)'),
    ], string='Múi giờ mặc định', default='Asia/Ho_Chi_Minh')
    
    country_code = fields.Char(
        string='Mã quốc gia',
        default='VN',
        help='Mã quốc gia mặc định'
    )
    
    # Danh sách module cần cài đặt
    module_ids = fields.Many2many(
        'database.template.module',
        string='Module cần cài đặt',
        help='Danh sách module sẽ được cài đặt tự động'
    )
    
    # Cấu hình demo data
    install_demo_data = fields.Boolean(
        string='Cài đặt dữ liệu demo',
        default=False,
        help='Có cài đặt dữ liệu demo hay không'
    )
    
    # Template mặc định
    is_default = fields.Boolean(
        string='Template mặc định',
        default=False,
        help='Đây có phải template mặc định không'
    )
    
    active = fields.Boolean(
        string='Kích hoạt',
        default=True,
        help='Template có được kích hoạt không'
    )

    # Relationship với module
    module_ids = fields.One2many(
        'database.template.module',
        'template_id',
        string='Module',
        help='Danh sách module sẽ được cài đặt'
    )
    
    @api.model
    def get_default_template(self):
        """Lấy template mặc định"""
        default_template = self.search([('is_default', '=', True)], limit=1)
        if not default_template:
            # Tạo template mặc định nếu chưa có
            default_template = self.create({
                'name': 'Template Mặc định',
                'description': 'Template cấu hình mặc định cho database mới',
                'is_default': True,
                'language': 'vi_VN',
                'timezone': 'Asia/Ho_Chi_Minh',
                'country_code': 'VN',
                'install_demo_data': False,
            })
        return default_template
    
    @api.model
    def create(self, vals):
        """Override create để đảm bảo chỉ có 1 template mặc định"""
        if vals.get('is_default'):
            # Bỏ default của các template khác
            self.search([('is_default', '=', True)]).write({'is_default': False})
        return super().create(vals)
    
    def write(self, vals):
        """Override write để đảm bảo chỉ có 1 template mặc định"""
        if vals.get('is_default'):
            # Bỏ default của các template khác
            self.search([('is_default', '=', True), ('id', 'not in', self.ids)]).write({'is_default': False})
        return super().write(vals)

    @api.model
    def create_default_templates(self):
        """Tạo các template mặc định"""
        default_templates = [
            {
                'name': 'Cơ bản',
                'description': 'Template cơ bản với các module thiết yếu',
                'language': 'vi_VN',
                'timezone': 'Asia/Ho_Chi_Minh',
                'country_code': 'VN',
                'install_demo_data': False,
                'is_default': True,
            },
            {
                'name': 'E-commerce',
                'description': 'Template cho website bán hàng',
                'language': 'vi_VN',
                'timezone': 'Asia/Ho_Chi_Minh',
                'country_code': 'VN',
                'install_demo_data': True,
                'is_default': False,
            },
            {
                'name': 'Kế toán',
                'description': 'Template cho hệ thống kế toán',
                'language': 'vi_VN',
                'timezone': 'Asia/Ho_Chi_Minh',
                'country_code': 'VN',
                'install_demo_data': False,
                'is_default': False,
            }
        ]

        for template_data in default_templates:
            existing = self.search([('name', '=', template_data['name'])])
            if not existing:
                template = self.create(template_data)

                # Thêm module cho từng template
                if template_data['name'] == 'Cơ bản':
                    modules = ['base', 'web', 'mail']
                elif template_data['name'] == 'E-commerce':
                    modules = ['base', 'web', 'mail', 'website', 'website_sale', 'sale', 'stock', 'account']
                elif template_data['name'] == 'Kế toán':
                    modules = ['base', 'web', 'mail', 'account', 'purchase', 'sale']

                for module_name in modules:
                    self.env['database.template.module'].create({
                        'template_id': template.id,
                        'name': module_name,
                        'description': f'Module {module_name}',
                        'required': module_name in ['base', 'web'],
                    })

    def action_apply_template(self):
        """Áp dụng template cho database mới"""
        return {
            'type': 'ir.actions.act_window',
            'name': _('Tạo Database từ Template'),
            'res_model': 'database.create.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_template_id': self.id,
                'default_language': self.language,
                'default_timezone': self.timezone,
                'default_country_code': self.country_code,
                'default_install_demo_data': self.install_demo_data,
            }
        }


class DatabaseTemplateModule(models.Model):
    _name = 'database.template.module'
    _description = 'Module trong Template'
    _order = 'category, name'

    template_id = fields.Many2one(
        'database.template',
        string='Template',
        required=True,
        ondelete='cascade',
        help='Template chứa module này'
    )
    name = fields.Char(
        string='Tên Module',
        required=True,
        help='Tên technical của module (vd: sale, purchase)'
    )
    display_name = fields.Char(
        string='Tên hiển thị',
        required=True,
        help='Tên hiển thị của module'
    )
    description = fields.Text(
        string='Mô tả',
        help='Mô tả về module'
    )
    category = fields.Selection([
        ('base', 'Cơ bản'),
        ('sales', 'Bán hàng'),
        ('purchase', 'Mua hàng'),
        ('inventory', 'Kho vận'),
        ('accounting', 'Kế toán'),
        ('hr', 'Nhân sự'),
        ('project', 'Dự án'),
        ('manufacturing', 'Sản xuất'),
        ('website', 'Website'),
        ('marketing', 'Marketing'),
        ('other', 'Khác'),
    ], string='Danh mục', default='other')
    
    required = fields.Boolean(
        string='Bắt buộc',
        default=False,
        help='Module này có bắt buộc phải cài không'
    )
    
    depends_on = fields.Char(
        string='Phụ thuộc',
        help='Các module phụ thuộc (cách nhau bởi dấu phẩy)'
    )
    
    active = fields.Boolean(
        string='Kích hoạt',
        default=True
    )
    
    @api.model
    def get_popular_modules(self):
        """Lấy danh sách module phổ biến"""
        popular_modules = [
            # Sales
            ('sale_management', 'Quản lý Bán hàng', 'sales', 'Quản lý quy trình bán hàng, báo giá, đơn hàng'),
            ('crm', 'CRM - Quản lý Khách hàng', 'sales', 'Quản lý khách hàng tiềm năng và cơ hội bán hàng'),
            
            # Purchase
            ('purchase', 'Quản lý Mua hàng', 'purchase', 'Quản lý quy trình mua hàng, yêu cầu báo giá'),
            
            # Inventory
            ('stock', 'Quản lý Kho', 'inventory', 'Quản lý kho vận, nhập xuất tồn'),
            
            # Accounting
            ('account', 'Kế toán', 'accounting', 'Quản lý kế toán, hóa đơn, thanh toán'),
            ('account_payment', 'Thanh toán', 'accounting', 'Quản lý các phương thức thanh toán'),
            
            # HR
            ('hr', 'Nhân sự', 'hr', 'Quản lý nhân viên, thông tin cá nhân'),
            ('hr_attendance', 'Chấm công', 'hr', 'Quản lý chấm công nhân viên'),
            
            # Project
            ('project', 'Quản lý Dự án', 'project', 'Quản lý dự án, task, thời gian'),
            
            # Website
            ('website', 'Website', 'website', 'Xây dựng website công ty'),
            ('website_sale', 'Bán hàng Online', 'website', 'Website thương mại điện tử'),
            
            # Other
            ('contacts', 'Danh bạ', 'other', 'Quản lý thông tin liên hệ'),
            ('calendar', 'Lịch', 'other', 'Quản lý lịch hẹn, sự kiện'),
        ]
        
        for module_data in popular_modules:
            name, display_name, category, description = module_data
            existing = self.search([('name', '=', name)])
            if not existing:
                self.create({
                    'name': name,
                    'display_name': display_name,
                    'category': category,
                    'description': description,
                })
        
        return self.search([])
