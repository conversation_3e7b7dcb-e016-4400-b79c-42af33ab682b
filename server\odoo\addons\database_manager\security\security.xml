<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Security Groups -->
    <record id="group_database_manager" model="res.groups">
        <field name="name">Database Manager</field>
        <field name="category_id" ref="base.module_category_administration"/>
        <field name="comment">Quản lý database - chỉ dành cho Administrator</field>
        <field name="implied_ids" eval="[(4, ref('base.group_system'))]"/>
        <field name="users" eval="[(4, ref('base.user_admin'))]"/>
    </record>

    <!-- Record Rules -->
    <record id="database_manager_rule" model="ir.rule">
        <field name="name">Database Manager Access Rule</field>
        <field name="model_id" ref="model_database_manager"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_database_manager'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="True"/>
    </record>

    <record id="database_template_rule" model="ir.rule">
        <field name="name">Database Template Access Rule</field>
        <field name="model_id" ref="model_database_template"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_database_manager'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="True"/>
    </record>

    <record id="database_template_module_rule" model="ir.rule">
        <field name="name">Database Template Module Access Rule</field>
        <field name="model_id" ref="model_database_template_module"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_database_manager'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="True"/>
    </record>
</odoo>
