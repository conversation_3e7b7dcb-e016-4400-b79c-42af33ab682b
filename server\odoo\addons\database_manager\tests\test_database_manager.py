# -*- coding: utf-8 -*-

import logging
from odoo.tests.common import TransactionCase
from odoo.exceptions import UserError, ValidationError

_logger = logging.getLogger(__name__)


class TestDatabaseManager(TransactionCase):
    
    def setUp(self):
        super().setUp()
        self.DatabaseManager = self.env['database.manager']
        self.DatabaseTemplate = self.env['database.template']
        self.DatabaseTemplateModule = self.env['database.template.module']
    
    def test_01_database_manager_creation(self):
        """Test tạo record database manager"""
        db_record = self.DatabaseManager.create({
            'name': 'test_db',
            'display_name': 'Test Database',
            'state': 'active',
            'admin_login': 'admin',
            'admin_name': 'Administrator',
            'admin_email': '<EMAIL>',
            'language': 'vi_VN',
            'timezone': 'Asia/Ho_Chi_Minh',
            'country_code': 'VN',
        })
        
        self.assertEqual(db_record.name, 'test_db')
        self.assertEqual(db_record.display_name, 'Test Database')
        self.assertEqual(db_record.state, 'active')
        self.assertFalse(db_record.is_current_db)  # Không phải database hiện tại
    
    def test_02_database_name_validation(self):
        """Test validation tên database"""
        # Tên hợp lệ
        valid_names = ['test_db', 'test-db', 'test.db', 'test123']
        for name in valid_names:
            db_record = self.DatabaseManager.create({
                'name': name,
                'display_name': f'Test {name}',
                'state': 'active',
            })
            self.assertEqual(db_record.name, name)
        
        # Tên không hợp lệ
        invalid_names = ['test db', 'test@db', 'test#db', 'test/db']
        for name in invalid_names:
            with self.assertRaises(ValidationError):
                self.DatabaseManager.create({
                    'name': name,
                    'display_name': f'Test {name}',
                    'state': 'active',
                })
    
    def test_03_get_database_list(self):
        """Test lấy danh sách database"""
        db_list = self.DatabaseManager.get_database_list()
        self.assertIsInstance(db_list, list)
        self.assertIn('ecomplus', db_list)  # Database hiện tại phải có trong danh sách
    
    def test_04_template_creation(self):
        """Test tạo template"""
        template = self.DatabaseTemplate.create({
            'name': 'Test Template',
            'description': 'Template for testing',
            'language': 'vi_VN',
            'timezone': 'Asia/Ho_Chi_Minh',
            'country_code': 'VN',
            'install_demo_data': False,
            'is_default': False,
        })
        
        self.assertEqual(template.name, 'Test Template')
        self.assertEqual(template.language, 'vi_VN')
        self.assertFalse(template.is_default)
    
    def test_05_template_default_constraint(self):
        """Test ràng buộc chỉ có 1 template mặc định"""
        # Tạo template đầu tiên làm default
        template1 = self.DatabaseTemplate.create({
            'name': 'Template 1',
            'description': 'First template',
            'is_default': True,
        })
        self.assertTrue(template1.is_default)
        
        # Tạo template thứ hai làm default
        template2 = self.DatabaseTemplate.create({
            'name': 'Template 2',
            'description': 'Second template',
            'is_default': True,
        })
        
        # Refresh để lấy dữ liệu mới
        template1.refresh()
        
        # Template 1 không còn là default
        self.assertFalse(template1.is_default)
        # Template 2 là default
        self.assertTrue(template2.is_default)
    
    def test_06_template_module_creation(self):
        """Test tạo module trong template"""
        template = self.DatabaseTemplate.create({
            'name': 'Test Template',
            'description': 'Template for testing',
        })
        
        module = self.DatabaseTemplateModule.create({
            'template_id': template.id,
            'name': 'sale',
            'display_name': 'Sales',
            'description': 'Sales module',
            'category': 'sales',
            'required': False,
        })
        
        self.assertEqual(module.template_id, template)
        self.assertEqual(module.name, 'sale')
        self.assertEqual(module.category, 'sales')
        self.assertFalse(module.required)
    
    def test_07_get_default_template(self):
        """Test lấy template mặc định"""
        # Xóa tất cả template hiện có
        self.DatabaseTemplate.search([]).unlink()
        
        # Gọi method get_default_template sẽ tự tạo template mặc định
        default_template = self.DatabaseTemplate.get_default_template()
        
        self.assertTrue(default_template.is_default)
        self.assertEqual(default_template.name, 'Template Mặc định')
        self.assertEqual(default_template.language, 'vi_VN')
    
    def test_08_database_size_calculation(self):
        """Test tính toán kích thước database"""
        # Tạo record database
        db_record = self.DatabaseManager.create({
            'name': 'ecomplus',  # Database hiện tại
            'display_name': 'Current Database',
            'state': 'active',
        })
        
        # Test method _get_database_size
        size = db_record._get_database_size('ecomplus')
        self.assertIsInstance(size, float)
        self.assertGreaterEqual(size, 0)
    
    def test_09_action_refresh_info(self):
        """Test action làm mới thông tin"""
        db_record = self.DatabaseManager.create({
            'name': 'ecomplus',
            'display_name': 'Current Database',
            'state': 'inactive',  # Đặt trạng thái sai
        })
        
        # Gọi action refresh
        db_record.action_refresh_info()
        
        # Kiểm tra trạng thái đã được cập nhật
        self.assertEqual(db_record.state, 'active')
        self.assertGreater(db_record.size, 0)
    
    def test_10_current_database_detection(self):
        """Test phát hiện database hiện tại"""
        # Tạo record cho database hiện tại
        current_db = self.DatabaseManager.create({
            'name': 'ecomplus',
            'display_name': 'Current Database',
            'state': 'active',
        })
        
        # Tạo record cho database khác
        other_db = self.DatabaseManager.create({
            'name': 'other_db',
            'display_name': 'Other Database',
            'state': 'active',
        })
        
        # Kiểm tra computed field is_current_db
        self.assertTrue(current_db.is_current_db)
        self.assertFalse(other_db.is_current_db)
