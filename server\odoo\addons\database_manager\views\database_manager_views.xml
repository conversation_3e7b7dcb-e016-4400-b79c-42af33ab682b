<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Database Manager List View -->
    <record id="view_database_manager_list" model="ir.ui.view">
        <field name="name">database.manager.list</field>
        <field name="model">database.manager</field>
        <field name="arch" type="xml">
            <list string="Danh sách Database" create="false" edit="false">
                <field name="name" string="Tên Database"/>
                <field name="display_name" string="Tên hiển thị"/>
                <field name="state" string="Trạng thái" widget="badge" 
                       decoration-success="state == 'active'"
                       decoration-warning="state == 'creating'"
                       decoration-danger="state == 'error'"
                       decoration-muted="state == 'inactive'"/>
                <field name="size" string="K<PERSON>ch thước (MB)" widget="float"/>
                <field name="created_date" string="Ngày tạo"/>
                <field name="admin_login" string="Admin Login"/>
                <field name="language" string="Ngôn ngữ"/>
                <field name="is_current_db" string="DB hiện tại" widget="boolean_toggle"/>
            </list>
        </field>
    </record>

    <!-- Database Manager Form View -->
    <record id="view_database_manager_form" model="ir.ui.view">
        <field name="name">database.manager.form</field>
        <field name="model">database.manager</field>
        <field name="arch" type="xml">
            <form string="Thông tin Database" create="false">
                <header>
                    <button name="action_refresh_info" type="object" string="Làm mới thông tin" 
                            class="btn-primary" icon="fa-refresh"/>
                    <button name="action_delete_database" type="object" string="Xóa Database" 
                            class="btn-danger" icon="fa-trash"
                            confirm="Bạn có chắc chắn muốn xóa database này không?"
                            invisible="is_current_db"/>
                    <field name="state" widget="statusbar" statusbar_visible="active,inactive,creating,error"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button class="oe_stat_button" type="object" name="action_refresh_info" icon="fa-database">
                            <field string="Kích thước" name="size" widget="statinfo"/>
                        </button>
                    </div>
                    
                    <div class="oe_title">
                        <h1>
                            <field name="name" readonly="1"/>
                        </h1>
                        <h2>
                            <field name="display_name" placeholder="Tên hiển thị..."/>
                        </h2>
                    </div>
                    
                    <group>
                        <group string="Thông tin cơ bản">
                            <field name="created_date" readonly="1"/>
                            <field name="last_backup_date" readonly="1"/>
                            <field name="is_current_db" readonly="1"/>
                            <field name="language"/>
                            <field name="timezone"/>
                            <field name="country_code"/>
                        </group>
                        <group string="Thông tin Admin">
                            <field name="admin_login"/>
                            <field name="admin_name"/>
                            <field name="admin_email" widget="email"/>
                        </group>
                    </group>
                    
                    <notebook>
                        <page string="Module đã cài đặt">
                            <field name="installed_modules" widget="text"/>
                        </page>
                        <page string="Ghi chú">
                            <field name="notes" widget="text" placeholder="Ghi chú về database này..."/>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Database Manager Search View -->
    <record id="view_database_manager_search" model="ir.ui.view">
        <field name="name">database.manager.search</field>
        <field name="model">database.manager</field>
        <field name="arch" type="xml">
            <search string="Tìm kiếm Database">
                <field name="name" string="Tên Database"/>
                <field name="display_name" string="Tên hiển thị"/>
                <field name="admin_login" string="Admin"/>
                <separator/>
                <filter string="Database hoạt động" name="active" domain="[('state', '=', 'active')]"/>
                <filter string="Database không hoạt động" name="inactive" domain="[('state', '=', 'inactive')]"/>
                <filter string="Database hiện tại" name="current" domain="[('is_current_db', '=', True)]"/>
                <separator/>
                <group expand="0" string="Nhóm theo">
                    <filter string="Trạng thái" name="group_state" context="{'group_by': 'state'}"/>
                    <filter string="Ngôn ngữ" name="group_language" context="{'group_by': 'language'}"/>
                    <filter string="Ngày tạo" name="group_date" context="{'group_by': 'created_date:month'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Database Manager Action -->
    <record id="action_database_manager" model="ir.actions.act_window">
        <field name="name">Quản lý Database</field>
        <field name="res_model">database.manager</field>
        <field name="view_mode">list,form</field>
        <field name="search_view_id" ref="view_database_manager_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Chưa có database nào được quản lý!
            </p>
            <p>
                Nhấn nút "Đồng bộ Database" để tải danh sách database từ hệ thống.
            </p>
        </field>
        <field name="context">{'search_default_active': 1}</field>
    </record>
</odoo>
