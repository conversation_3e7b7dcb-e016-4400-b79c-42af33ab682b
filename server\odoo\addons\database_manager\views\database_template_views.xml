<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Database Template List View -->
    <record id="view_database_template_list" model="ir.ui.view">
        <field name="name">database.template.list</field>
        <field name="model">database.template</field>
        <field name="arch" type="xml">
            <list string="Template cấu hình Database">
                <field name="name" string="Tên Template"/>
                <field name="description" string="Mô tả"/>
                <field name="language" string="Ngôn ngữ"/>
                <field name="timezone" string="Múi giờ"/>
                <field name="is_default" string="Mặc định" widget="boolean_toggle"/>
                <field name="active" string="Kích hoạt" widget="boolean_toggle"/>
            </list>
        </field>
    </record>

    <!-- Database Template Form View -->
    <record id="view_database_template_form" model="ir.ui.view">
        <field name="name">database.template.form</field>
        <field name="model">database.template</field>
        <field name="arch" type="xml">
            <form string="Template cấu hình Database">
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button class="oe_stat_button" type="object" name="action_apply_template" icon="fa-play">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_text">Áp dụng</span>
                                <span class="o_stat_text">Template</span>
                            </div>
                        </button>
                    </div>
                    
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Tên template..."/>
                        </h1>
                    </div>
                    
                    <group>
                        <group string="Cấu hình cơ bản">
                            <field name="language"/>
                            <field name="timezone"/>
                            <field name="country_code"/>
                            <field name="install_demo_data"/>
                        </group>
                        <group string="Tùy chọn">
                            <field name="is_default"/>
                            <field name="active"/>
                        </group>
                    </group>
                    
                    <notebook>
                        <page string="Mô tả">
                            <field name="description" widget="text" placeholder="Mô tả về template này..."/>
                        </page>
                        <page string="Module cần cài đặt">
                            <field name="module_ids" widget="many2many">
                                <list editable="bottom">
                                    <field name="name"/>
                                    <field name="display_name"/>
                                    <field name="category"/>
                                    <field name="description"/>
                                    <field name="required" widget="boolean_toggle"/>
                                </list>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Database Template Module List View -->
    <record id="view_database_template_module_list" model="ir.ui.view">
        <field name="name">database.template.module.list</field>
        <field name="model">database.template.module</field>
        <field name="arch" type="xml">
            <list string="Module Template">
                <field name="name" string="Tên Module"/>
                <field name="display_name" string="Tên hiển thị"/>
                <field name="category" string="Danh mục"/>
                <field name="description" string="Mô tả"/>
                <field name="required" string="Bắt buộc" widget="boolean_toggle"/>
                <field name="active" string="Kích hoạt" widget="boolean_toggle"/>
            </list>
        </field>
    </record>

    <!-- Database Template Module Form View -->
    <record id="view_database_template_module_form" model="ir.ui.view">
        <field name="name">database.template.module.form</field>
        <field name="model">database.template.module</field>
        <field name="arch" type="xml">
            <form string="Module Template">
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="display_name" placeholder="Tên hiển thị module..."/>
                        </h1>
                        <h2>
                            <field name="name" placeholder="Tên technical module..."/>
                        </h2>
                    </div>
                    
                    <group>
                        <group string="Thông tin cơ bản">
                            <field name="category"/>
                            <field name="required"/>
                            <field name="active"/>
                        </group>
                        <group string="Phụ thuộc">
                            <field name="depends_on" placeholder="module1, module2, ..."/>
                        </group>
                    </group>
                    
                    <group string="Mô tả">
                        <field name="description" widget="text" nolabel="1" placeholder="Mô tả về module này..."/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Actions -->
    <record id="action_database_template" model="ir.actions.act_window">
        <field name="name">Database Templates</field>
        <field name="res_model">database.template</field>
        <field name="view_mode">list,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No templates created yet!
            </p>
            <p>
                Create templates to save default configurations for new databases.
            </p>
        </field>
    </record>

    <record id="action_database_template_module" model="ir.actions.act_window">
        <field name="name">Template Modules</field>
        <field name="res_model">database.template.module</field>
        <field name="view_mode">list,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No template modules configured yet!
            </p>
            <p>
                Add modules to templates for automatic installation when creating new databases.
            </p>
        </field>
    </record>
</odoo>
