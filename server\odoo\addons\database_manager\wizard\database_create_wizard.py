# -*- coding: utf-8 -*-

import re
import logging
from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
from odoo.service import db
from odoo.tools import config

_logger = logging.getLogger(__name__)


class DatabaseCreateWizard(models.TransientModel):
    _name = 'database.create.wizard'
    _description = 'Wizard tạo Database mới'

    # Bước 1: Thông tin cơ bản
    step = fields.Selection([
        ('basic', 'Thông tin cơ bản'),
        ('admin', 'Cấu hình Admin'),
        ('modules', 'Chọn Module'),
        ('confirm', 'Xác nhận'),
    ], string='Bước', default='basic', required=True)

    # Thông tin database
    database_name = fields.Char(
        string='Tên Database',
        required=True,
        help='Tên database (chỉ chứa chữ, số, gạch dưới, gạch ngang)'
    )
    database_display_name = fields.Char(
        string='Tên hiển thị',
        help='Tên hiển thị cho database'
    )
    
    # Template
    template_id = fields.Many2one(
        'database.template',
        string='Template cấu hình',
        help='Chọn template cấu hình có sẵn'
    )
    
    # Cấu hình cơ bản
    language = fields.Selection([
        ('vi_VN', 'Tiếng Việt'),
        ('en_US', 'English'),
        ('fr_FR', 'Français'),
        ('de_DE', 'Deutsch'),
        ('es_ES', 'Español'),
    ], string='Ngôn ngữ', default='vi_VN', required=True)
    
    timezone = fields.Selection([
        ('Asia/Ho_Chi_Minh', 'Việt Nam (UTC+7)'),
        ('UTC', 'UTC'),
        ('America/New_York', 'New York (UTC-5)'),
        ('Europe/London', 'London (UTC+0)'),
        ('Asia/Tokyo', 'Tokyo (UTC+9)'),
    ], string='Múi giờ', default='Asia/Ho_Chi_Minh', required=True)
    
    country_code = fields.Char(
        string='Mã quốc gia',
        default='VN',
        help='Mã quốc gia (VN, US, FR, etc.)'
    )
    
    # Thông tin admin
    admin_login = fields.Char(
        string='Tên đăng nhập Admin',
        default='admin',
        required=True,
        help='Tên đăng nhập của admin'
    )
    admin_password = fields.Char(
        string='Mật khẩu Admin',
        required=True,
        help='Mật khẩu của admin'
    )
    admin_password_confirm = fields.Char(
        string='Xác nhận mật khẩu',
        required=True,
        help='Nhập lại mật khẩu để xác nhận'
    )
    admin_name = fields.Char(
        string='Tên đầy đủ Admin',
        required=True,
        help='Tên đầy đủ của admin'
    )
    admin_email = fields.Char(
        string='Email Admin',
        help='Email của admin'
    )
    admin_phone = fields.Char(
        string='Số điện thoại',
        help='Số điện thoại của admin'
    )
    
    # Module selection
    module_ids = fields.Many2many(
        'database.template.module',
        string='Module cần cài đặt',
        help='Chọn các module sẽ được cài đặt tự động'
    )
    
    install_demo_data = fields.Boolean(
        string='Cài đặt dữ liệu demo',
        default=False,
        help='Có cài đặt dữ liệu demo hay không'
    )
    
    # Master password
    master_password = fields.Char(
        string='Master Password',
        required=True,
        help='Master password của Odoo server'
    )
    
    # Progress tracking
    creation_progress = fields.Text(
        string='Tiến trình tạo',
        readonly=True,
        help='Thông tin về tiến trình tạo database'
    )
    
    @api.onchange('template_id')
    def _onchange_template_id(self):
        """Áp dụng cấu hình từ template"""
        if self.template_id:
            self.language = self.template_id.language
            self.timezone = self.template_id.timezone
            self.country_code = self.template_id.country_code
            self.install_demo_data = self.template_id.install_demo_data
            self.module_ids = self.template_id.module_ids
    
    @api.constrains('database_name')
    def _check_database_name(self):
        """Kiểm tra tên database hợp lệ"""
        for record in self:
            if record.database_name:
                # Kiểm tra pattern
                if not re.match(r'^[a-zA-Z0-9_.-]+$', record.database_name):
                    raise ValidationError(_(
                        "Tên database chỉ được chứa chữ cái, số, dấu gạch dưới (_), "
                        "dấu gạch ngang (-) và dấu chấm (.)"
                    ))
                
                # Kiểm tra database đã tồn tại
                existing_dbs = db.list_dbs(force=True)
                if record.database_name in existing_dbs:
                    raise ValidationError(_(
                        "Database '%s' đã tồn tại. Vui lòng chọn tên khác."
                    ) % record.database_name)
    
    @api.constrains('admin_password', 'admin_password_confirm')
    def _check_password_match(self):
        """Kiểm tra mật khẩu khớp nhau"""
        for record in self:
            if record.admin_password and record.admin_password_confirm:
                if record.admin_password != record.admin_password_confirm:
                    raise ValidationError(_("Mật khẩu xác nhận không khớp!"))
    
    def action_next_step(self):
        """Chuyển sang bước tiếp theo"""
        if self.step == 'basic':
            self.step = 'admin'
        elif self.step == 'admin':
            self.step = 'modules'
        elif self.step == 'modules':
            self.step = 'confirm'
        
        return {
            'type': 'ir.actions.act_window',
            'res_model': 'database.create.wizard',
            'res_id': self.id,
            'view_mode': 'form',
            'target': 'new',
            'context': self.env.context,
        }
    
    def action_previous_step(self):
        """Quay lại bước trước"""
        if self.step == 'admin':
            self.step = 'basic'
        elif self.step == 'modules':
            self.step = 'admin'
        elif self.step == 'confirm':
            self.step = 'modules'
        
        return {
            'type': 'ir.actions.act_window',
            'res_model': 'database.create.wizard',
            'res_id': self.id,
            'view_mode': 'form',
            'target': 'new',
            'context': self.env.context,
        }
    
    def action_create_database(self):
        """Tạo database mới"""
        try:
            # Kiểm tra master password
            if not config.verify_admin_password(self.master_password):
                raise UserError(_("Master password không đúng!"))

            # Tạo database ngay lập tức (blocking)
            self._create_database_sync()

            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Thành công'),
                    'message': _('Database "%s" đã được tạo thành công!') % self.database_name,
                    'type': 'success',
                    'sticky': False,
                }
            }

        except Exception as e:
            _logger.error(f"Lỗi khi tạo database: {e}")
            raise UserError(_("Lỗi khi tạo database: %s") % str(e))

    def _create_database_sync(self):
        """Tạo database đồng bộ"""
        try:
            _logger.info(f"Bắt đầu tạo database: {self.database_name}")

            # Tạo database sử dụng Odoo service API
            db.exp_create_database(
                self.database_name,
                self.install_demo_data,
                self.language,
                self.admin_password,
                self.admin_login,
                self.country_code,
                self.admin_phone or ''
            )

            _logger.info(f"Database {self.database_name} đã được tạo thành công")

            # Tạo record trong database.manager của database hiện tại
            self.env['database.manager'].create({
                'name': self.database_name,
                'display_name': self.database_display_name or self.database_name,
                'state': 'active',
                'admin_login': self.admin_login,
                'admin_name': self.admin_name,
                'admin_email': self.admin_email,
                'language': self.language,
                'timezone': self.timezone,
                'country_code': self.country_code,
                'notes': f"Tạo bởi wizard vào {fields.Datetime.now()}",
            })

            # Cài đặt module nếu được chọn
            if self.module_ids:
                self._install_modules()

        except Exception as e:
            _logger.error(f"Lỗi trong quá trình tạo database: {e}")
            raise

    def _install_modules(self):
        """Cài đặt module vào database mới"""
        try:
            _logger.info(f"Bắt đầu cài đặt {len(self.module_ids)} module vào database {self.database_name}")

            # Kết nối đến database mới
            import odoo.modules.registry
            registry = odoo.modules.registry.Registry.new(self.database_name)

            with registry.cursor() as cr:
                env = api.Environment(cr, 1, {})  # SUPERUSER_ID = 1

                # Cài đặt từng module
                for module in self.module_ids:
                    try:
                        # Tìm module trong ir.module.module
                        module_obj = env['ir.module.module'].search([('name', '=', module.name)])
                        if module_obj:
                            if module_obj.state == 'uninstalled':
                                module_obj.button_immediate_install()
                                _logger.info(f"Đã cài đặt module: {module.name}")
                            else:
                                _logger.info(f"Module {module.name} đã được cài đặt")
                        else:
                            _logger.warning(f"Không tìm thấy module: {module.name}")
                    except Exception as e:
                        _logger.error(f"Lỗi khi cài đặt module {module.name}: {e}")

                cr.commit()

        except Exception as e:
            _logger.error(f"Lỗi khi cài đặt module: {e}")
            # Không raise error vì database đã được tạo thành công
    
    def action_close(self):
        """Đóng wizard"""
        return {'type': 'ir.actions.act_window_close'}
