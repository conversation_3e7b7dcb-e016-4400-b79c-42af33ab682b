<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Database Create Wizard Form View -->
    <record id="view_database_create_wizard_form" model="ir.ui.view">
        <field name="name">database.create.wizard.form</field>
        <field name="model">database.create.wizard</field>
        <field name="arch" type="xml">
            <form string="Tạo Database mới">
                <header>
                    <!-- Step Basic -->
                    <button name="action_next_step" type="object" string="Tiếp theo" 
                            class="btn-primary" invisible="step != 'basic'"/>
                    
                    <!-- Step Admin -->
                    <button name="action_previous_step" type="object" string="Quay lại" 
                            class="btn-secondary" invisible="step != 'admin'"/>
                    <button name="action_next_step" type="object" string="Tiếp theo" 
                            class="btn-primary" invisible="step != 'admin'"/>
                    
                    <!-- Step Modules -->
                    <button name="action_previous_step" type="object" string="Quay lại" 
                            class="btn-secondary" invisible="step != 'modules'"/>
                    <button name="action_next_step" type="object" string="Tiếp theo" 
                            class="btn-primary" invisible="step != 'modules'"/>
                    
                    <!-- Step Confirm -->
                    <button name="action_previous_step" type="object" string="Quay lại" 
                            class="btn-secondary" invisible="step != 'confirm'"/>
                    <button name="action_create_database" type="object" string="Tạo Database" 
                            class="btn-success" invisible="step != 'confirm'"/>
                    
                    <button name="action_close" type="object" string="Đóng" 
                            class="btn-secondary"/>
                </header>
                
                <sheet>
                    <!-- Progress indicator -->
                    <div class="o_form_header_horizontal d-flex flex-wrap align-items-center justify-content-start gap-2 mb-2">
                        <span class="badge badge-primary" invisible="step not in ['basic', 'admin', 'modules', 'confirm']">
                            Bước 1: Thông tin cơ bản
                        </span>
                        <span class="badge" invisible="step in ['basic']">
                            <span invisible="step not in ['admin', 'modules', 'confirm']" class="badge-primary">Bước 2: Cấu hình Admin</span>
                            <span invisible="step in ['admin', 'modules', 'confirm']" class="badge-secondary">Bước 2: Cấu hình Admin</span>
                        </span>
                        <span class="badge" invisible="step in ['basic', 'admin']">
                            <span invisible="step not in ['modules', 'confirm']" class="badge-primary">Bước 3: Chọn Module</span>
                            <span invisible="step in ['modules', 'confirm']" class="badge-secondary">Bước 3: Chọn Module</span>
                        </span>
                        <span class="badge" invisible="step in ['basic', 'admin', 'modules']">
                            <span invisible="step != 'confirm'" class="badge-primary">Bước 4: Xác nhận</span>
                            <span invisible="step == 'confirm'" class="badge-secondary">Bước 4: Xác nhận</span>
                        </span>
                    </div>
                    
                    <!-- Step 1: Basic Information -->
                    <div invisible="step != 'basic'">
                        <h2>Thông tin cơ bản Database</h2>
                        <group>
                            <group string="Thông tin Database">
                                <field name="database_name" placeholder="ten_database"/>
                                <field name="database_display_name" placeholder="Tên hiển thị..."/>
                                <field name="template_id" placeholder="Chọn template có sẵn..."/>
                            </group>
                            <group string="Cấu hình địa phương">
                                <field name="language"/>
                                <field name="timezone"/>
                                <field name="country_code"/>
                            </group>
                        </group>
                    </div>
                    
                    <!-- Step 2: Admin Configuration -->
                    <div invisible="step != 'admin'">
                        <h2>Cấu hình tài khoản Admin</h2>
                        <group>
                            <group string="Thông tin đăng nhập">
                                <field name="admin_login"/>
                                <field name="admin_password" password="True"/>
                                <field name="admin_password_confirm" password="True"/>
                            </group>
                            <group string="Thông tin cá nhân">
                                <field name="admin_name"/>
                                <field name="admin_email" widget="email"/>
                                <field name="admin_phone"/>
                            </group>
                        </group>
                    </div>
                    
                    <!-- Step 3: Module Selection -->
                    <div invisible="step != 'modules'">
                        <h2>Chọn Module cần cài đặt</h2>
                        <group>
                            <field name="install_demo_data"/>
                        </group>
                        <field name="module_ids" widget="many2many_checkboxes">
                            <list>
                                <field name="display_name"/>
                                <field name="category"/>
                                <field name="description"/>
                            </list>
                        </field>
                    </div>
                    
                    <!-- Step 4: Confirmation -->
                    <div invisible="step != 'confirm'">
                        <h2>Xác nhận thông tin</h2>
                        <group>
                            <group string="Thông tin Database">
                                <label for="database_name" string="Tên Database:"/>
                                <div><field name="database_name" readonly="1" nolabel="1"/></div>
                                <label for="language" string="Ngôn ngữ:"/>
                                <div><field name="language" readonly="1" nolabel="1"/></div>
                                <label for="timezone" string="Múi giờ:"/>
                                <div><field name="timezone" readonly="1" nolabel="1"/></div>
                            </group>
                            <group string="Thông tin Admin">
                                <label for="admin_login" string="Tên đăng nhập:"/>
                                <div><field name="admin_login" readonly="1" nolabel="1"/></div>
                                <label for="admin_name" string="Tên đầy đủ:"/>
                                <div><field name="admin_name" readonly="1" nolabel="1"/></div>
                                <label for="admin_email" string="Email:"/>
                                <div><field name="admin_email" readonly="1" nolabel="1"/></div>
                            </group>
                        </group>
                        
                        <group string="Xác thực">
                            <field name="master_password" password="True" 
                                   placeholder="Nhập master password của Odoo server"/>
                        </group>
                        
                        <div class="alert alert-warning" role="alert">
                            <strong>Chú ý:</strong> Quá trình tạo database có thể mất vài phút. 
                            Vui lòng không đóng cửa sổ này trong quá trình tạo.
                        </div>
                    </div>
                    
                    <!-- Progress Display -->
                    <div invisible="not creation_progress">
                        <h2>Tiến trình tạo Database</h2>
                        <field name="creation_progress" widget="text" readonly="1"/>
                    </div>
                    
                    <field name="step" invisible="1"/>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Database Delete Wizard Form View -->
    <record id="view_database_delete_wizard_form" model="ir.ui.view">
        <field name="name">database.delete.wizard.form</field>
        <field name="model">database.delete.wizard</field>
        <field name="arch" type="xml">
            <form string="Xóa Database">
                <header>
                    <button name="action_delete_database" type="object" string="Xác nhận xóa" 
                            class="btn-danger"/>
                    <button name="action_cancel" type="object" string="Hủy bỏ" 
                            class="btn-secondary"/>
                </header>
                
                <sheet>
                    <div class="alert alert-danger" role="alert">
                        <h4><i class="fa fa-warning"/> Cảnh báo!</h4>
                        <p>Bạn đang chuẩn bị xóa database <strong><field name="database_name" readonly="1"/></strong>.</p>
                        <p>Hành động này <strong>KHÔNG THỂ HOÀN TÁC</strong>. Tất cả dữ liệu sẽ bị mất vĩnh viễn.</p>
                    </div>
                    
                    <group>
                        <group string="Xác nhận">
                            <field name="confirm_name" placeholder="Nhập tên database để xác nhận"/>
                            <field name="master_password" password="True" 
                                   placeholder="Master password của Odoo server"/>
                        </group>
                        <group string="Tùy chọn">
                            <field name="backup_before_delete"/>
                        </group>
                    </group>
                    
                    <group string="Lý do xóa">
                        <field name="reason" widget="text" nolabel="1" 
                               placeholder="Ghi rõ lý do xóa database này..."/>
                    </group>
                    
                    <field name="database_id" invisible="1"/>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Database Sync Wizard Form View -->
    <record id="view_database_sync_wizard_form" model="ir.ui.view">
        <field name="name">database.sync.wizard.form</field>
        <field name="model">database.sync.wizard</field>
        <field name="arch" type="xml">
            <form string="Đồng bộ Database">
                <header>
                    <button name="action_sync_databases" type="object" string="Bắt đầu đồng bộ" 
                            class="btn-primary" invisible="sync_result"/>
                    <button name="action_close" type="object" string="Đóng" 
                            class="btn-secondary"/>
                </header>
                
                <sheet>
                    <div invisible="sync_result">
                        <h2>Cấu hình đồng bộ Database</h2>
                        <group>
                            <group string="Loại đồng bộ">
                                <field name="sync_type" widget="radio"/>
                            </group>
                            <group string="Tùy chọn">
                                <field name="update_size"/>
                                <field name="remove_missing"/>
                            </group>
                        </group>
                        
                        <div class="alert alert-info" role="alert">
                            <h5>Thông tin về các loại đồng bộ:</h5>
                            <ul>
                                <li><strong>Đồng bộ đầy đủ:</strong> Thêm database mới, cập nhật thông tin và đánh dấu database không tồn tại</li>
                                <li><strong>Chỉ thêm database mới:</strong> Chỉ thêm các database chưa có trong hệ thống</li>
                                <li><strong>Chỉ cập nhật thông tin:</strong> Chỉ cập nhật thông tin của database đã có</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div invisible="not sync_result">
                        <h2>Kết quả đồng bộ</h2>
                        <field name="sync_result" widget="text" readonly="1"/>
                    </div>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Actions -->
    <record id="action_database_create_wizard" model="ir.actions.act_window">
        <field name="name">Tạo Database mới</field>
        <field name="res_model">database.create.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>

    <record id="action_database_sync" model="ir.actions.act_window">
        <field name="name">Đồng bộ Database</field>
        <field name="res_model">database.sync.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>
</odoo>
