# -*- coding: utf-8 -*-

import logging
from odoo import models, fields, api, _
from odoo.exceptions import UserError
from odoo.service import db
from odoo.tools import config

_logger = logging.getLogger(__name__)


class DatabaseDeleteWizard(models.TransientModel):
    _name = 'database.delete.wizard'
    _description = 'Wizard xóa Database'

    database_id = fields.Many2one(
        'database.manager',
        string='Database',
        required=True,
        help='Database cần xóa'
    )
    
    database_name = fields.Char(
        string='Tên Database',
        related='database_id.name',
        readonly=True
    )
    
    confirm_name = fields.Char(
        string='Xác nhận tên Database',
        required=True,
        help='Nhập tên database để xác nhận xóa'
    )
    
    master_password = fields.Char(
        string='Master Password',
        required=True,
        help='Master password của Odoo server'
    )
    
    backup_before_delete = fields.Boolean(
        string='Backup trước khi xóa',
        default=True,
        help='Tạo backup trước khi xóa database'
    )
    
    reason = fields.Text(
        string='Lý do xóa',
        help='<PERSON>hi rõ lý do xóa database này'
    )
    
    @api.constrains('confirm_name', 'database_name')
    def _check_confirm_name(self):
        """Kiểm tra tên xác nhận"""
        for record in self:
            if record.confirm_name and record.database_name:
                if record.confirm_name != record.database_name:
                    raise UserError(_(
                        "Tên xác nhận không khớp với tên database. "
                        "Vui lòng nhập chính xác: %s"
                    ) % record.database_name)
    
    def action_delete_database(self):
        """Xóa database"""
        try:
            # Kiểm tra master password
            if not config.verify_admin_password(self.master_password):
                raise UserError(_("Master password không đúng!"))

            # Kiểm tra database hiện tại
            if self.database_id.is_current_db:
                raise UserError(_("Không thể xóa database hiện tại đang sử dụng!"))

            # Kiểm tra database có tồn tại không
            existing_dbs = db.list_dbs(force=True)
            if self.database_name not in existing_dbs:
                raise UserError(_("Database '%s' không tồn tại!") % self.database_name)

            # Backup nếu được yêu cầu
            backup_path = None
            if self.backup_before_delete:
                backup_path = self._create_backup()

            # Log lại thao tác
            _logger.info(f"Xóa database {self.database_name}. Lý do: {self.reason}")

            # Xóa database sử dụng Odoo service API
            result = db.exp_drop(self.database_name)

            if not result:
                raise UserError(_("Không thể xóa database. Vui lòng kiểm tra lại."))

            # Cập nhật trạng thái record
            notes = f"Đã xóa vào {fields.Datetime.now()}. Lý do: {self.reason}"
            if backup_path:
                notes += f"\nBackup tại: {backup_path}"

            self.database_id.write({
                'state': 'inactive',
                'notes': notes
            })

            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Thành công'),
                    'message': _('Database "%s" đã được xóa thành công!') % self.database_name,
                    'type': 'success',
                    'sticky': False,
                }
            }

        except Exception as e:
            _logger.error(f"Lỗi khi xóa database {self.database_name}: {e}")
            raise UserError(_("Lỗi khi xóa database: %s") % str(e))
    
    def _create_backup(self):
        """Tạo backup database"""
        try:
            import tempfile
            import os
            from datetime import datetime

            # Tạo tên file backup
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"{self.database_name}_backup_{timestamp}.zip"

            # Tạo thư mục backup
            backup_dir = config.get('backup_dir', os.path.join(os.path.expanduser('~'), 'odoo_backups'))
            if not os.path.exists(backup_dir):
                os.makedirs(backup_dir)

            final_path = os.path.join(backup_dir, backup_filename)

            # Tạo backup sử dụng Odoo service API
            with open(final_path, 'wb') as backup_file:
                db.dump_db(self.database_name, backup_file, 'zip')

            _logger.info(f"Backup database {self.database_name} tại: {final_path}")
            return final_path

        except Exception as e:
            _logger.warning(f"Không thể tạo backup cho {self.database_name}: {e}")
            # Không raise error vì backup chỉ là tùy chọn
            return None
    
    def action_cancel(self):
        """Hủy bỏ"""
        return {'type': 'ir.actions.act_window_close'}
