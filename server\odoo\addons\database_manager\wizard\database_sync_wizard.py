# -*- coding: utf-8 -*-

import logging
from odoo import models, fields, api, _
from odoo.exceptions import UserError

_logger = logging.getLogger(__name__)


class DatabaseSyncWizard(models.TransientModel):
    _name = 'database.sync.wizard'
    _description = 'Wizard đồng bộ Database'

    sync_type = fields.Selection([
        ('full', 'Đồng bộ đầy đủ'),
        ('new_only', 'Chỉ thêm database mới'),
        ('update_only', 'Chỉ cập nhật thông tin'),
    ], string='Loại đồng bộ', default='full', required=True)
    
    update_size = fields.Boolean(
        string='Cập nhật kích thước',
        default=True,
        help='Cập nhật kích thước của các database'
    )
    
    remove_missing = fields.Boolean(
        string='Đánh dấu database không tồn tại',
        default=True,
        help='Đánh dấu inactive các database không còn tồn tại'
    )
    
    sync_result = fields.Text(
        string='Kết quả đồng bộ',
        readonly=True,
        help='Thông tin về kết quả đồng bộ'
    )
    
    def action_sync_databases(self):
        """Thực hiện đồng bộ database"""
        try:
            result_lines = []
            
            # Lấy danh sách database từ PostgreSQL
            db_manager = self.env['database.manager']
            pg_databases = db_manager.get_database_list()
            existing_records = db_manager.search([])
            existing_names = existing_records.mapped('name')
            
            result_lines.append(f"Tìm thấy {len(pg_databases)} database trong PostgreSQL")
            result_lines.append(f"Có {len(existing_records)} record trong hệ thống")
            result_lines.append("")
            
            # Thêm database mới
            if self.sync_type in ['full', 'new_only']:
                new_databases = [db for db in pg_databases if db not in existing_names]
                result_lines.append(f"Database mới cần thêm: {len(new_databases)}")
                
                for db_name in new_databases:
                    try:
                        size = db_manager._get_database_size(db_name) if self.update_size else 0.0
                        db_manager.create({
                            'name': db_name,
                            'display_name': db_name,
                            'state': 'active',
                            'size': size,
                        })
                        result_lines.append(f"  + Thêm: {db_name}")
                    except Exception as e:
                        result_lines.append(f"  ! Lỗi khi thêm {db_name}: {str(e)}")
            
            # Cập nhật thông tin database hiện có
            if self.sync_type in ['full', 'update_only']:
                update_count = 0
                for record in existing_records:
                    if record.name in pg_databases:
                        updates = {}
                        
                        # Cập nhật trạng thái
                        if record.state != 'active':
                            updates['state'] = 'active'
                        
                        # Cập nhật kích thước
                        if self.update_size:
                            new_size = db_manager._get_database_size(record.name)
                            if abs(new_size - record.size) > 0.1:  # Chỉ update nếu thay đổi > 0.1MB
                                updates['size'] = new_size
                        
                        if updates:
                            record.write(updates)
                            update_count += 1
                
                result_lines.append(f"Cập nhật thông tin: {update_count} database")
            
            # Đánh dấu database không tồn tại
            if self.sync_type == 'full' and self.remove_missing:
                missing_records = existing_records.filtered(lambda r: r.name not in pg_databases and r.state == 'active')
                if missing_records:
                    missing_records.write({'state': 'inactive'})
                    result_lines.append(f"Đánh dấu inactive: {len(missing_records)} database")
                    for record in missing_records:
                        result_lines.append(f"  - {record.name}")
            
            result_lines.append("")
            result_lines.append("Đồng bộ hoàn tất!")
            
            self.sync_result = "\n".join(result_lines)
            
            return {
                'type': 'ir.actions.act_window',
                'res_model': 'database.sync.wizard',
                'res_id': self.id,
                'view_mode': 'form',
                'target': 'new',
                'context': dict(self.env.context, show_result=True),
            }
            
        except Exception as e:
            _logger.error(f"Lỗi khi đồng bộ database: {e}")
            raise UserError(_("Lỗi khi đồng bộ database: %s") % str(e))
    
    def action_close(self):
        """Đóng wizard và refresh danh sách"""
        return {
            'type': 'ir.actions.act_window',
            'res_model': 'database.manager',
            'view_mode': 'list,form',
            'target': 'current',
        }
