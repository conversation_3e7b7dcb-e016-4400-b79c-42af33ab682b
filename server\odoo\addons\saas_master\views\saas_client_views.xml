<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- SaaS Client List View -->
    <record id="view_saas_client_list" model="ir.ui.view">
        <field name="name">saas.client.list</field>
        <field name="model">saas.client</field>
        <field name="arch" type="xml">
            <list string="Khách Hàng SaaS" default_order="name" class="o_list_view_sidebar_responsive">
                <field name="name" string="Tên <PERSON>" width="150px"/>
                <field name="email" widget="email" string="Email" width="180px"/>
                <field name="company_name" string="Công Ty" width="150px"/>
                <field name="plan_id" string="Gói Dịch Vụ" width="120px"/>
                <field name="state" widget="badge" string="Trạng Thái" width="100px"
                       decoration-success="state == 'active'"
                       decoration-warning="state == 'suspended'"
                       decoration-danger="state == 'cancelled'"
                       decoration-info="state == 'draft'"/>
                <field name="partner_id" string="Đố<PERSON>" width="120px"/>
                <field name="instance_count" string="Instances" width="80px"/>
            </list>
        </field>
    </record>

    <!-- SaaS Client Form View -->
    <record id="view_saas_client_form" model="ir.ui.view">
        <field name="name">saas.client.form</field>
        <field name="model">saas.client</field>
        <field name="arch" type="xml">
            <form string="Khách Hàng SaaS" class="o_form_view_sidebar_optimized">
                <header>
                    <div class="d-flex flex-wrap align-items-center justify-content-start gap-2 mb-2">
                        <button name="action_activate" string="Kích Hoạt" type="object"
                                class="btn btn-primary me-2" invisible="state == 'active'"/>
                        <button name="action_suspend" string="Tạm Dừng" type="object"
                                class="btn btn-warning me-2" invisible="state != 'active'"/>
                        <button name="action_cancel" string="Hủy" type="object"
                                class="btn btn-danger me-2" invisible="state == 'cancelled'"/>
                        <button name="action_reset_to_draft" string="Đặt Lại Nháp" type="object"
                                class="btn btn-secondary me-2" invisible="state == 'draft'"/>
                    </div>
                    <field name="state" widget="statusbar" statusbar_visible="draft,active,suspended,cancelled"/>
                </header>
                <sheet class="o_form_sheet_sidebar_responsive">
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_instances" type="object" class="oe_stat_button" icon="fa-database">
                            <field name="instance_count" widget="statinfo" string="Instances"/>
                        </button>
                    </div>

                    <!-- Main Information Tab Layout -->
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Tên khách hàng..." class="o_text_overflow"/>
                        </h1>
                    </div>

                    <div class="row">
                        <div class="col-12 col-md-6">
                            <group string="Thông Tin Liên Hệ" name="contact_info">
                                <field name="email" widget="email" placeholder="<EMAIL>"/>
                                <field name="phone" widget="phone" placeholder="+84 xxx xxx xxx"/>
                                <field name="company_name" placeholder="Tên công ty..."/>
                            </group>
                        </div>
                        <div class="col-12 col-md-6">
                            <group string="Thông Tin Dịch Vụ" name="service_info">
                                <field name="plan_id" options="{'no_create': True, 'no_edit': True}"/>
                                <field name="partner_id" readonly="1" string="Đối Tác Liên Quan"/>
                                <field name="active"/>
                            </group>
                        </div>
                    </div>

                    <notebook>
                        <page string="Ghi Chú" name="notes">
                            <field name="notes" placeholder="Ghi chú bổ sung về khách hàng..." nolabel="1"/>
                        </page>
                        <page string="Instances" name="instances">
                            <field name="instance_ids" nolabel="1">
                                <list string="Instances Khách Hàng" editable="bottom" class="o_list_view_sidebar_responsive">
                                    <field name="name" string="Tên Instance" width="150px"/>
                                    <field name="subdomain" string="Subdomain" width="120px"/>
                                    <field name="state" widget="badge" string="Trạng Thái" width="100px"/>
                                    <field name="created_date" string="Ngày Tạo" width="120px"/>
                                    <field name="expiry_date" string="Ngày Hết Hạn" width="120px"/>
                                </list>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!-- SaaS Client Search View -->
    <record id="view_saas_client_search" model="ir.ui.view">
        <field name="name">saas.client.search</field>
        <field name="model">saas.client</field>
        <field name="arch" type="xml">
            <search string="Tìm Kiếm Khách Hàng SaaS">
                <field name="name" string="Tên Khách Hàng" filter_domain="['|', ('name', 'ilike', self), ('company_name', 'ilike', self)]"/>
                <field name="email" string="Email"/>
                <field name="company_name" string="Tên Công Ty"/>
                <field name="plan_id" string="Gói Dịch Vụ"/>
                <separator/>
                <filter string="Đang Hoạt Động" name="active" domain="[('active', '=', True)]" help="Khách hàng đang hoạt động"/>
                <filter string="Không Hoạt Động" name="inactive" domain="[('active', '=', False)]" help="Khách hàng không hoạt động"/>
                <separator/>
                <filter string="Nháp" name="draft" domain="[('state', '=', 'draft')]" help="Khách hàng ở trạng thái nháp"/>
                <filter string="Hoạt Động" name="active_clients" domain="[('state', '=', 'active')]" help="Khách hàng đang hoạt động"/>
                <filter string="Tạm Dừng" name="suspended" domain="[('state', '=', 'suspended')]" help="Khách hàng bị tạm dừng"/>
                <filter string="Đã Hủy" name="cancelled" domain="[('state', '=', 'cancelled')]" help="Khách hàng đã hủy"/>
                <separator/>
                <filter string="Có Instances" name="has_instances" domain="[('instance_ids', '!=', False)]" help="Khách hàng có instances"/>
                <group expand="0" string="Nhóm Theo">
                    <filter string="Trạng Thái" name="group_state" context="{'group_by': 'state'}"/>
                    <filter string="Gói Dịch Vụ" name="group_plan" context="{'group_by': 'plan_id'}"/>
                    <filter string="Công Ty" name="group_company" context="{'group_by': 'company_name'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- SaaS Client Kanban View -->
    <record id="view_saas_client_kanban" model="ir.ui.view">
        <field name="name">saas.client.kanban</field>
        <field name="model">saas.client</field>
        <field name="arch" type="xml">
            <kanban class="o_kanban_mobile o_kanban_sidebar_responsive" default_group_by="state">
                <field name="id"/>
                <field name="name"/>
                <field name="email"/>
                <field name="company_name"/>
                <field name="state"/>
                <field name="plan_id"/>
                <field name="instance_count"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_card oe_kanban_global_click">
                            <div class="o_kanban_card_header">
                                <div class="o_kanban_card_header_title">
                                    <div class="o_primary">
                                        <strong><t t-esc="record.name.value"/></strong>
                                    </div>
                                    <div class="o_secondary" t-if="record.company_name.value">
                                        <t t-esc="record.company_name.value"/>
                                    </div>
                                </div>
                                <div class="o_kanban_manage_button_section">
                                    <a class="o_kanban_manage_toggle_button" href="#" tabindex="-1">
                                        <i class="fa fa-ellipsis-v" role="img" aria-label="Manage" title="Manage"/>
                                    </a>
                                </div>
                            </div>
                            <div class="o_kanban_card_content">
                                <div class="o_kanban_card_manage_pane dropdown-menu" role="menu">
                                    <a role="menuitem" type="edit" class="dropdown-item">Chỉnh Sửa</a>
                                    <a role="menuitem" type="delete" class="dropdown-item">Xóa</a>
                                </div>
                                <div class="row">
                                    <div class="col-6">
                                        <button class="btn btn-primary btn-sm" name="action_view_instances" type="object">
                                            <t t-esc="record.instance_count.value"/> Instances
                                        </button>
                                    </div>
                                    <div class="col-6 text-right">
                                        <span class="badge badge-pill" t-attf-class="badge-#{record.state.raw_value === 'active' ? 'success' : record.state.raw_value === 'suspended' ? 'warning' : record.state.raw_value === 'cancelled' ? 'danger' : 'info'}">
                                            <t t-esc="record.state.value"/>
                                        </span>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <i class="fa fa-envelope"/> <t t-esc="record.email.value"/>
                                </div>
                                <div t-if="record.plan_id.value">
                                    <i class="fa fa-cube"/> <t t-esc="record.plan_id.value"/>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- SaaS Client Action -->
    <record id="action_saas_client" model="ir.actions.act_window">
        <field name="name">Khách Hàng SaaS</field>
        <field name="res_model">saas.client</field>
        <field name="view_mode">kanban,list,form</field>
        <field name="search_view_id" ref="view_saas_client_search"/>
        <field name="context">{'search_default_active': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Tạo khách hàng SaaS đầu tiên của bạn!
            </p>
            <p>
                Quản lý khách hàng SaaS và đăng ký của họ từ đây.
                Bạn có thể theo dõi thông tin khách hàng, quản lý trạng thái và xử lý gói dịch vụ của họ.
            </p>
        </field>
    </record>

    <!-- Menu Items -->
    <menuitem id="menu_saas_client"
              name="Khách Hàng"
              parent="menu_saas_master_root"
              action="action_saas_client"
              sequence="10"/>
</odoo>
