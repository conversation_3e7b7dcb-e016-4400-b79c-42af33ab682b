<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- SaaS Plan List View -->
    <record id="view_saas_plan_list" model="ir.ui.view">
        <field name="name">saas.plan.list</field>
        <field name="model">saas.plan</field>
        <field name="arch" type="xml">
            <list string="Gói Dịch Vụ SaaS" default_order="sequence,name" class="o_list_view_sidebar_responsive">
                <field name="sequence" widget="handle" width="30px"/>
                <field name="name" string="Tên Gói" width="150px"/>
                <field name="code" string="Mã Gói" width="80px"/>
                <field name="price" widget="monetary" options="{'currency_field': 'currency_id'}" string="Giá" width="100px"/>
                <field name="currency_id" column_invisible="1"/>
                <field name="billing_cycle" string="<PERSON> K<PERSON> Toán" width="120px"/>
                <field name="max_users" string="Số User Tối <PERSON>" width="100px"/>
                <field name="client_count" string="Khách <PERSON>ng" width="80px"/>
                <field name="state" widget="badge" string="Trạng Thái" width="100px"
                       decoration-success="state == 'active'"
                       decoration-warning="state == 'deprecated'"
                       decoration-danger="state == 'archived'"
                       decoration-info="state == 'draft'"/>
            </list>
        </field>
    </record>

    <!-- SaaS Plan Form View -->
    <record id="view_saas_plan_form" model="ir.ui.view">
        <field name="name">saas.plan.form</field>
        <field name="model">saas.plan</field>
        <field name="arch" type="xml">
            <form string="Gói Dịch Vụ SaaS" class="o_form_view_sidebar_optimized">
                <header>
                    <div class="d-flex flex-wrap align-items-center justify-content-start gap-2 mb-2">
                        <button name="action_activate" string="Kích Hoạt" type="object"
                                class="btn btn-primary me-2" invisible="state == 'active'"/>
                        <button name="action_deprecate" string="Lỗi Thời" type="object"
                                class="btn btn-warning me-2" invisible="state != 'active'"/>
                        <button name="action_archive_plan" string="Lưu Trữ" type="object"
                                class="btn btn-danger me-2" invisible="state == 'archived'"/>
                        <button name="action_reset_to_draft" string="Đặt Lại Nháp" type="object"
                                class="btn btn-secondary me-2" invisible="state == 'draft'"/>
                    </div>
                    <field name="state" widget="statusbar" statusbar_visible="draft,active,deprecated,archived"/>
                </header>
                <sheet class="o_form_sheet_sidebar_responsive">
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_clients" type="object" class="oe_stat_button" icon="fa-users">
                            <field name="client_count" widget="statinfo" string="Khách Hàng"/>
                        </button>
                        <button name="action_view_instances" type="object" class="oe_stat_button" icon="fa-database">
                            <field name="instance_count" widget="statinfo" string="Instances"/>
                        </button>
                    </div>

                    <!-- Main Information -->
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Tên gói dịch vụ..." class="o_text_overflow"/>
                        </h1>
                    </div>

                    <div class="row">
                        <div class="col-12 col-md-6">
                            <group string="Thông Tin Cơ Bản" name="basic_info">
                                <field name="code" placeholder="MA_GOI_DICH_VU"/>
                                <field name="sequence"/>
                                <field name="active"/>
                            </group>
                        </div>
                        <div class="col-12 col-md-6">
                            <group string="Thông Tin Giá" name="pricing_info">
                                <field name="price" widget="monetary" options="{'currency_field': 'currency_id'}"/>
                                <field name="currency_id"/>
                                <field name="billing_cycle"/>
                            </group>
                        </div>
                    </div>

                    <notebook>
                        <page string="Chi Tiết Gói" name="plan_details">
                            <div class="row">
                                <div class="col-12 col-md-6">
                                    <group string="Giới Hạn" name="limits">
                                        <field name="max_users"/>
                                        <field name="max_storage_gb"/>
                                        <field name="max_databases"/>
                                    </group>
                                </div>
                                <div class="col-12 col-md-6">
                                    <group string="Tính Năng" name="features">
                                        <field name="has_custom_domain"/>
                                        <field name="has_ssl"/>
                                        <field name="has_backup"/>
                                        <field name="has_support"/>
                                        <field name="support_level" invisible="not has_support"/>
                                    </group>
                                </div>
                            </div>
                            <group>
                                <field name="description" placeholder="Mô tả chi tiết về gói dịch vụ..." nolabel="1"/>
                                <field name="features" placeholder="Danh sách các tính năng bao gồm trong gói này..." nolabel="1"/>
                            </group>
                        </page>
                        <page string="Kỹ Thuật" name="technical">
                            <group>
                                <field name="template_db"/>
                                <field name="server_id"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!-- SaaS Plan Kanban View -->
    <record id="view_saas_plan_kanban" model="ir.ui.view">
        <field name="name">saas.plan.kanban</field>
        <field name="model">saas.plan</field>
        <field name="arch" type="xml">
            <kanban class="o_kanban_mobile o_kanban_sidebar_responsive" default_order="sequence,name">
                <field name="name"/>
                <field name="code"/>
                <field name="price"/>
                <field name="currency_id"/>
                <field name="billing_cycle"/>
                <field name="state"/>
                <field name="client_count"/>
                <templates>
                    <t t-name="card">
                        <div class="oe_kanban_content">
                            <div class="row">
                                <div class="col-6">
                                    <strong><field name="name"/></strong>
                                </div>
                                <div class="col-6 text-end">
                                    <field name="state" widget="badge"/>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <span class="text-muted">Mã: </span><field name="code"/>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <span class="text-muted">Giá: </span>
                                    <field name="price" widget="monetary" options="{'currency_field': 'currency_id'}"/>
                                    <span class="text-muted"> / </span><field name="billing_cycle"/>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <span class="text-muted">Khách Hàng: </span><field name="client_count"/>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- SaaS Plan Search View -->
    <record id="view_saas_plan_search" model="ir.ui.view">
        <field name="name">saas.plan.search</field>
        <field name="model">saas.plan</field>
        <field name="arch" type="xml">
            <search string="Tìm Kiếm Gói Dịch Vụ SaaS">
                <field name="name" string="Tên Gói" filter_domain="['|', ('name', 'ilike', self), ('code', 'ilike', self)]"/>
                <field name="code" string="Mã Gói"/>
                <field name="description" string="Mô Tả"/>
                <separator/>
                <filter string="Đang Hoạt Động" name="active" domain="[('active', '=', True)]" help="Gói dịch vụ đang hoạt động"/>
                <filter string="Không Hoạt Động" name="inactive" domain="[('active', '=', False)]" help="Gói dịch vụ không hoạt động"/>
                <separator/>
                <filter string="Nháp" name="draft" domain="[('state', '=', 'draft')]" help="Gói dịch vụ ở trạng thái nháp"/>
                <filter string="Hoạt Động" name="active_plans" domain="[('state', '=', 'active')]" help="Gói dịch vụ đang hoạt động"/>
                <filter string="Lỗi Thời" name="deprecated" domain="[('state', '=', 'deprecated')]" help="Gói dịch vụ lỗi thời"/>
                <filter string="Lưu Trữ" name="archived" domain="[('state', '=', 'archived')]" help="Gói dịch vụ đã lưu trữ"/>
                <separator/>
                <filter string="Hàng Tháng" name="monthly" domain="[('billing_cycle', '=', 'monthly')]" help="Thanh toán hàng tháng"/>
                <filter string="Hàng Năm" name="yearly" domain="[('billing_cycle', '=', 'yearly')]" help="Thanh toán hàng năm"/>
                <group expand="0" string="Nhóm Theo">
                    <filter string="Trạng Thái" name="group_state" context="{'group_by': 'state'}"/>
                    <filter string="Chu Kỳ Thanh Toán" name="group_billing" context="{'group_by': 'billing_cycle'}"/>
                    <filter string="Mức Độ Hỗ Trợ" name="group_support" context="{'group_by': 'support_level'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- SaaS Plan Action -->
    <record id="action_saas_plan" model="ir.actions.act_window">
        <field name="name">Gói Dịch Vụ SaaS</field>
        <field name="res_model">saas.plan</field>
        <field name="view_mode">kanban,list,form</field>
        <field name="search_view_id" ref="view_saas_plan_search"/>
        <field name="context">{'search_default_active': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Tạo gói dịch vụ SaaS đầu tiên của bạn!
            </p>
            <p>
                Định nghĩa các gói dịch vụ với tính năng, giá cả và giới hạn khác nhau.
                Gói dịch vụ giúp bạn tổ chức các dịch vụ SaaS và quản lý đăng ký của khách hàng.
            </p>
        </field>
    </record>

    <!-- Menu Items -->
    <menuitem id="menu_saas_plan"
              name="Gói Dịch Vụ"
              parent="menu_saas_master_root"
              action="action_saas_plan"
              sequence="20"/>
</odoo>
