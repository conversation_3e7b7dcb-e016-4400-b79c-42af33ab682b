#!/usr/bin/env python3
import xmlrpc.client
import sys

url = 'http://localhost:8069'
db = 'ecomplus'
username = 'admin'
password = 'admin'

try:
    print("Connecting to Odoo...")
    common = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/common')
    uid = common.authenticate(db, username, password, {})
    
    if not uid:
        print("Authentication failed!")
        sys.exit(1)
        
    print(f"Connected as user ID: {uid}")
    
    models = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/object')
    
    # Check module
    module = models.execute_kw(db, uid, password,
        'ir.module.module', 'search_read',
        [[('name', '=', 'database_manager')]],
        {'fields': ['name', 'state']})
    
    if module:
        print(f"Module state: {module[0]['state']}")
    else:
        print("Module not found!")
        
    # Check menu
    menu = models.execute_kw(db, uid, password,
        'ir.ui.menu', 'search_read',
        [[('name', '=', 'Database Manager')]],
        {'fields': ['name', 'parent_id']})
    
    if menu:
        print(f"Menu found: {menu[0]['name']}")
        print(f"Parent: {menu[0]['parent_id']}")
    else:
        print("Menu not found!")
        
except Exception as e:
    print(f"Error: {e}")
    sys.exit(1)
