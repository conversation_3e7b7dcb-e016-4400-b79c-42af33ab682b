#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script để kiểm tra module database_manager
"""

import xmlrpc.client

# C<PERSON>u hình kết nối
url = 'http://localhost:8069'
db = 'ecomplus'
username = 'admin'
password = 'admin'

# Kết nối
common = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/common')
uid = common.authenticate(db, username, password, {})

if uid:
    print(f"✅ Kết nối thành công! User ID: {uid}")
    
    models = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/object')
    
    # Test 1: Kiểm tra model database.manager
    try:
        db_managers = models.execute_kw(db, uid, password,
            'database.manager', 'search_read', [[]], {'fields': ['name', 'state']})
        print(f"✅ Model database.manager hoạt động! T<PERSON><PERSON> thấy {len(db_managers)} record(s)")
        for db_record in db_managers:
            print(f"   - {db_record['name']}: {db_record['state']}")
    except Exception as e:
        print(f"❌ Lỗi model database.manager: {e}")
    
    # Test 2: Kiểm tra model database.template
    try:
        templates = models.execute_kw(db, uid, password,
            'database.template', 'search_read', [[]], {'fields': ['name', 'is_default']})
        print(f"✅ Model database.template hoạt động! Tìm thấy {len(templates)} template(s)")
        for template in templates:
            print(f"   - {template['name']}: {'Mặc định' if template['is_default'] else 'Thường'}")
    except Exception as e:
        print(f"❌ Lỗi model database.template: {e}")
    
    # Test 3: Kiểm tra model database.template.module
    try:
        modules = models.execute_kw(db, uid, password,
            'database.template.module', 'search_read', [[]], {'fields': ['name', 'display_name', 'category']})
        print(f"✅ Model database.template.module hoạt động! Tìm thấy {len(modules)} module(s)")
        for module in modules[:5]:  # Chỉ hiển thị 5 module đầu
            print(f"   - {module['name']} ({module['display_name']}): {module['category']}")
    except Exception as e:
        print(f"❌ Lỗi model database.template.module: {e}")
    
    # Test 4: Kiểm tra menu
    try:
        menus = models.execute_kw(db, uid, password,
            'ir.ui.menu', 'search_read', [
                [('name', 'ilike', 'Database')]
            ], {'fields': ['name', 'parent_id']})
        print(f"✅ Menu Database hoạt động! Tìm thấy {len(menus)} menu(s)")
        for menu in menus:
            parent = menu['parent_id'][1] if menu['parent_id'] else 'Root'
            print(f"   - {menu['name']} (Parent: {parent})")
    except Exception as e:
        print(f"❌ Lỗi menu: {e}")
    
    print("\n🎉 Test hoàn thành!")
    
else:
    print("❌ Không thể kết nối đến Odoo!")
