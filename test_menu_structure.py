#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script để verify menu structure của Database Manager module
"""

import xmlrpc.client

# C<PERSON>u hình kết nối
url = 'http://localhost:8069'
db = 'ecomplus'
username = 'admin'
password = 'admin'

def test_menu_structure():
    """Test menu structure và access rights"""
    
    print("🔍 Testing Database Manager Menu Structure...")
    print("=" * 60)
    
    try:
        # Kết nối đến Odoo
        common = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/common')
        uid = common.authenticate(db, username, password, {})
        
        if not uid:
            print("❌ Authentication failed!")
            return False
            
        print(f"✅ Connected to Odoo as user ID: {uid}")
        
        # Tạo object proxy
        models = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/object')
        
        # 1. Test root menu item
        print("\n📋 Testing Root Menu Item...")
        root_menu = models.execute_kw(db, uid, password,
            'ir.ui.menu', 'search_read',
            [[('id', '=', 'database_manager.menu_database_manager_root')]],
            {'fields': ['name', 'sequence', 'web_icon', 'parent_id']})
        
        if root_menu:
            menu = root_menu[0]
            print(f"✅ Root Menu Found:")
            print(f"   - Name: {menu['name']}")
            print(f"   - Sequence: {menu['sequence']}")
            print(f"   - Icon: {menu['web_icon']}")
            print(f"   - Parent: {menu['parent_id']}")
        else:
            print("❌ Root menu not found!")
            return False
        
        # 2. Test submenu items
        print("\n📋 Testing Submenu Items...")
        submenus = models.execute_kw(db, uid, password,
            'ir.ui.menu', 'search_read',
            [[('parent_id', '=', 'database_manager.menu_database_manager_root')]],
            {'fields': ['name', 'sequence', 'action', 'parent_id']})
        
        expected_submenus = [
            'Databases',
            'Create Database', 
            'Templates',
            'Template Modules',
            'Sync Databases'
        ]
        
        found_menus = []
        for submenu in submenus:
            found_menus.append(submenu['name'])
            print(f"✅ Submenu: {submenu['name']} (seq: {submenu['sequence']})")
            
        # Kiểm tra tất cả submenu có đủ không
        missing_menus = set(expected_submenus) - set(found_menus)
        if missing_menus:
            print(f"❌ Missing submenus: {missing_menus}")
            return False
        else:
            print("✅ All expected submenus found!")
        
        # 3. Test actions
        print("\n📋 Testing Actions...")
        actions_to_test = [
            'database_manager.action_database_manager',
            'database_manager.action_database_create_wizard',
            'database_manager.action_database_template',
            'database_manager.action_database_template_module',
            'database_manager.action_database_sync'
        ]
        
        for action_id in actions_to_test:
            action = models.execute_kw(db, uid, password,
                'ir.actions.act_window', 'search_read',
                [[('id', '=', action_id)]],
                {'fields': ['name', 'res_model', 'view_mode']})
            
            if action:
                act = action[0]
                print(f"✅ Action: {act['name']} -> {act['res_model']} ({act['view_mode']})")
            else:
                print(f"❌ Action not found: {action_id}")
                return False
        
        # 4. Test security groups
        print("\n🔒 Testing Security Groups...")
        group = models.execute_kw(db, uid, password,
            'res.groups', 'search_read',
            [[('id', '=', 'database_manager.group_database_manager')]],
            {'fields': ['name', 'category_id', 'implied_ids']})
        
        if group:
            grp = group[0]
            print(f"✅ Security Group: {grp['name']}")
            print(f"   - Category: {grp['category_id']}")
            print(f"   - Implied Groups: {grp['implied_ids']}")
        else:
            print("❌ Security group not found!")
            return False
        
        # 5. Test models accessibility
        print("\n📊 Testing Model Access...")
        models_to_test = [
            'database.manager',
            'database.template', 
            'database.template.module',
            'database.create.wizard',
            'database.sync.wizard'
        ]
        
        for model_name in models_to_test:
            try:
                # Test read access
                count = models.execute_kw(db, uid, password,
                    model_name, 'search_count', [[]])
                print(f"✅ Model {model_name}: {count} records accessible")
            except Exception as e:
                print(f"❌ Model {model_name}: Access denied - {str(e)}")
                return False
        
        print("\n" + "=" * 60)
        print("🎉 All tests passed! Database Manager menu structure is working correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {str(e)}")
        return False

if __name__ == '__main__':
    success = test_menu_structure()
    exit(0 if success else 1)
